#!/bin/bash

# Quotese Sitemap修复验证脚本
# 用于验证robots.txt修复和sitemap.xml可访问性
# 生成时间：2024年6月24日

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 网站URL
SITE_URL="https://quotese.com"
ROBOTS_URL="${SITE_URL}/robots.txt"
SITEMAP_URL="${SITE_URL}/sitemap.xml"

echo "========================================"
echo "Quotese Sitemap修复验证"
echo "========================================"
echo "网站: $SITE_URL"
echo "时间: $(date)"
echo ""

# 1. 检查robots.txt
log_info "1. 检查robots.txt配置..."

# 获取robots.txt内容
ROBOTS_CONTENT=$(curl -s "$ROBOTS_URL")
if [ $? -eq 0 ]; then
    log_success "robots.txt可访问"
    
    # 检查是否还有XML阻止规则
    if echo "$ROBOTS_CONTENT" | grep -q "^Disallow: /\*\.xml"; then
        log_error "发现XML阻止规则仍然存在！"
        echo "$ROBOTS_CONTENT" | grep -n "xml"
        exit 1
    else
        log_success "XML阻止规则已被移除"
    fi
    
    # 检查sitemap声明
    if echo "$ROBOTS_CONTENT" | grep -q "Sitemap:"; then
        SITEMAP_LINE=$(echo "$ROBOTS_CONTENT" | grep "Sitemap:")
        log_success "找到sitemap声明: $SITEMAP_LINE"
    else
        log_warning "未找到sitemap声明"
    fi
else
    log_error "无法访问robots.txt"
    exit 1
fi

echo ""

# 2. 检查sitemap.xml可访问性
log_info "2. 检查sitemap.xml可访问性..."

# 检查HTTP状态码
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$SITEMAP_URL")
if [ "$HTTP_STATUS" = "200" ]; then
    log_success "sitemap.xml HTTP状态码: $HTTP_STATUS"
else
    log_error "sitemap.xml HTTP状态码异常: $HTTP_STATUS"
    exit 1
fi

# 检查Content-Type
CONTENT_TYPE=$(curl -s -I "$SITEMAP_URL" | grep -i "content-type" | cut -d' ' -f2- | tr -d '\r\n')
if echo "$CONTENT_TYPE" | grep -q "xml"; then
    log_success "Content-Type正确: $CONTENT_TYPE"
else
    log_warning "Content-Type可能有问题: $CONTENT_TYPE"
fi

echo ""

# 3. 验证XML格式
log_info "3. 验证XML格式..."

# 下载并验证XML
SITEMAP_CONTENT=$(curl -s "$SITEMAP_URL")
if [ $? -eq 0 ]; then
    # 检查XML声明
    if echo "$SITEMAP_CONTENT" | head -1 | grep -q "<?xml"; then
        log_success "XML声明正确"
    else
        log_error "XML声明缺失或错误"
    fi
    
    # 使用xmllint验证（如果可用）
    if command -v xmllint >/dev/null 2>&1; then
        if echo "$SITEMAP_CONTENT" | xmllint --noout - 2>/dev/null; then
            log_success "XML格式验证通过"
        else
            log_error "XML格式验证失败"
            exit 1
        fi
    else
        log_warning "xmllint不可用，跳过XML格式验证"
    fi
else
    log_error "无法下载sitemap.xml"
    exit 1
fi

echo ""

# 4. 统计URL数量
log_info "4. 统计sitemap内容..."

URL_COUNT=$(echo "$SITEMAP_CONTENT" | grep -c "<loc>")
log_success "总URL数量: $URL_COUNT"

# 按类型统计
HOME_COUNT=$(echo "$SITEMAP_CONTENT" | grep -c "https://quotese.com/$")
AUTHOR_COUNT=$(echo "$SITEMAP_CONTENT" | grep -c "/authors/")
CATEGORY_COUNT=$(echo "$SITEMAP_CONTENT" | grep -c "/categories/")
SOURCE_COUNT=$(echo "$SITEMAP_CONTENT" | grep -c "/sources/")

echo "  - 首页: $HOME_COUNT"
echo "  - 作者详情页: $AUTHOR_COUNT"
echo "  - 分类详情页: $CATEGORY_COUNT"
echo "  - 来源详情页: $SOURCE_COUNT"

echo ""

# 5. 测试几个URL的可访问性
log_info "5. 测试URL可访问性..."

# 测试首页
if curl -s -o /dev/null -w "%{http_code}" "$SITE_URL/" | grep -q "200"; then
    log_success "首页可访问"
else
    log_warning "首页可能有问题"
fi

# 测试一个作者页面
AUTHOR_URL=$(echo "$SITEMAP_CONTENT" | grep "/authors/" | head -1 | sed 's/.*<loc>\(.*\)<\/loc>.*/\1/')
if [ -n "$AUTHOR_URL" ]; then
    if curl -s -o /dev/null -w "%{http_code}" "$AUTHOR_URL" | grep -q "200"; then
        log_success "作者页面可访问: $(basename "$AUTHOR_URL")"
    else
        log_warning "作者页面可能有问题: $AUTHOR_URL"
    fi
fi

echo ""

# 6. 生成报告
log_info "6. 生成验证报告..."

REPORT_FILE="sitemap_verification_$(date +%Y%m%d_%H%M%S).txt"

cat > "$REPORT_FILE" << EOF
Quotese Sitemap修复验证报告
生成时间: $(date)
网站: $SITE_URL

=== 验证结果 ===
robots.txt状态: 正常
sitemap.xml状态: 正常
XML格式: 有效
总URL数量: $URL_COUNT

=== URL分布 ===
首页: $HOME_COUNT
作者详情页: $AUTHOR_COUNT
分类详情页: $CATEGORY_COUNT
来源详情页: $SOURCE_COUNT

=== 修复状态 ===
XML阻止规则: 已移除
sitemap声明: 存在
HTTP状态码: 200
Content-Type: $CONTENT_TYPE

=== 建议操作 ===
1. 在Google Search Console中重新提交sitemap
2. 等待24-48小时查看抓取状态
3. 监控索引URL数量变化
4. 跟踪搜索流量改善情况
EOF

log_success "验证报告已生成: $REPORT_FILE"

echo ""
echo "========================================"
log_success "✅ Sitemap修复验证完成！"
echo "========================================"
echo ""
echo "📋 下一步操作："
echo "1. 登录Google Search Console"
echo "2. 重新提交sitemap.xml"
echo "3. 等待24-48小时查看结果"
echo "4. 监控搜索引擎索引效果"
echo ""
