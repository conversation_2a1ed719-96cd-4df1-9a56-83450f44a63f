"""
Quotese Sitemap配置文件
包含sitemap生成的所有配置参数和SEO设置
版本：v2.0 - SEO重启实施
更新日期：2025年6月16日
"""

import os
from typing import Dict, Any


class SitemapConfig:
    """Sitemap生成配置类"""
    
    # ==================== 基础配置 ====================
    
    # 网站基础信息
    BASE_URL = "https://quotese.com"
    SITE_NAME = "Quotese.com"
    
    # 文件路径配置
    OUTPUT_PATH = "../frontend/sitemap.xml"
    LOG_FILE = "sitemap_generation.log"
    BACKUP_DIR = "sitemap_backups"
    
    # ==================== URL路径配置 ====================
    
    PATHS = {
        'AUTHORS': 'authors',
        'CATEGORIES': 'categories', 
        'SOURCES': 'sources',
        'QUOTES': 'quotes',
        'SEARCH': 'search'
    }
    
    SUBPATHS = {
        'QUOTES': 'quotes'
    }
    
    # ==================== SEO配置 ====================
    
    SEO_CONFIG = {
        # 首页
        'home': {
            'priority': '1.0',
            'changefreq': 'daily',
            'description': '网站首页，最高优先级'
        },
        
        # 列表页面
        'list_pages': {
            'priority': '0.9',
            'changefreq': 'weekly',
            'description': '作者、类别、来源、名言列表页面'
        },
        
        # 作者详情页
        'author_detail': {
            'priority': '0.8',
            'changefreq': 'weekly',
            'description': '作者详情页面和作者名言列表页面'
        },
        
        # 类别详情页
        'category_detail': {
            'priority': '0.8',
            'changefreq': 'weekly',
            'description': '类别详情页面和类别名言列表页面'
        },
        
        # 来源详情页
        'source_detail': {
            'priority': '0.7',
            'changefreq': 'monthly',
            'description': '来源详情页面'
        },
        
        # 名言详情页
        'quote_detail': {
            'priority': '0.6',
            'changefreq': 'monthly',
            'description': '名言详情页面'
        },
        
        # 搜索页面
        'search': {
            'priority': '0.5',
            'changefreq': 'weekly',
            'description': '搜索页面'
        }
    }
    
    # ==================== URL类型启用配置 ====================

    # URL类型启用/禁用开关
    # 根据生产环境实际可访问状态配置
    URL_TYPES_ENABLED = {
        # 核心页面 - 当前可访问
        'home': True,                    # 首页 /
        'author_detail': True,           # 作者详情页 /authors/{slug}/
        'category_detail': True,         # 类别详情页 /categories/{slug}/
        'source_detail': True,           # 来源详情页 /sources/{slug}/

        # 列表页面 - 当前不可访问，临时禁用
        'authors_list': False,           # 作者列表页 /authors/
        'categories_list': False,        # 类别列表页 /categories/
        'sources_list': False,           # 来源列表页 /sources/
        'quotes_list': False,            # 名言列表页 /quotes/

        # 子页面 - 当前不可访问，临时禁用
        'author_quotes': False,          # 作者名言页 /authors/{slug}/quotes/
        'category_quotes': False,        # 类别名言页 /categories/{slug}/quotes/

        # 名言详情页 - 当前不可访问，临时禁用
        'quote_detail': False,           # 名言详情页 /quotes/{id}/

        # 搜索页面 - 当前不可访问，临时禁用
        'search': False                  # 搜索页面 /search/
    }

    # URL类型描述（用于日志和文档）
    URL_TYPES_DESCRIPTION = {
        'home': '网站首页',
        'author_detail': '作者详情页面',
        'category_detail': '类别详情页面',
        'source_detail': '来源详情页面',
        'authors_list': '作者列表页面',
        'categories_list': '类别列表页面',
        'sources_list': '来源列表页面',
        'quotes_list': '名言列表页面',
        'author_quotes': '作者名言列表页面',
        'category_quotes': '类别名言列表页面',
        'quote_detail': '名言详情页面',
        'search': '搜索页面'
    }

    # ==================== 限制配置 ====================

    # 数量限制
    MAX_QUOTES = 50000  # 限制名言数量，避免sitemap过大
    MAX_AUTHORS = 10000  # 限制作者数量
    MAX_CATEGORIES = 1000  # 限制类别数量
    MAX_SOURCES = 5000  # 限制来源数量

    # 文件大小限制
    MAX_URLS_PER_SITEMAP = 50000  # 每个sitemap文件的最大URL数量
    MAX_FILE_SIZE_MB = 50  # 最大文件大小（MB）
    
    # ==================== 搜索引擎配置 ====================
    
    SEARCH_ENGINES = {
        'google': {
            'ping_url': 'http://www.google.com/ping?sitemap=',
            'name': 'Google Search Console',
            'enabled': True
        },
        'bing': {
            'ping_url': 'http://www.bing.com/ping?sitemap=',
            'name': 'Bing Webmaster Tools',
            'enabled': True
        },
        'yandex': {
            'ping_url': 'http://webmaster.yandex.com/ping?sitemap=',
            'name': 'Yandex Webmaster',
            'enabled': False  # 可选启用
        }
    }
    
    # ==================== 备份配置 ====================
    
    BACKUP_CONFIG = {
        'enabled': True,
        'retention_days': 30,  # 保留30天的备份
        'max_backups': 100,    # 最多保留100个备份文件
        'compress': False      # 是否压缩备份文件
    }
    
    # ==================== 日志配置 ====================
    
    LOG_CONFIG = {
        'level': 'INFO',
        'format': '%(asctime)s - %(levelname)s - %(message)s',
        'file_enabled': True,
        'console_enabled': True,
        'max_file_size_mb': 10,
        'backup_count': 5
    }
    
    # ==================== 验证配置 ====================
    
    VALIDATION_CONFIG = {
        'xml_validation': True,     # 是否验证XML格式
        'url_validation': True,     # 是否验证URL格式
        'size_validation': True,    # 是否验证文件大小
        'count_validation': True    # 是否验证URL数量
    }
    
    # ==================== 性能配置 ====================
    
    PERFORMANCE_CONFIG = {
        'batch_size': 1000,         # 批处理大小
        'memory_limit_mb': 512,     # 内存限制
        'timeout_seconds': 300,     # 超时时间
        'parallel_processing': False # 是否启用并行处理
    }
    
    # ==================== 自定义配置方法 ====================
    
    @classmethod
    def get_seo_config(cls, page_type: str) -> Dict[str, Any]:
        """
        获取指定页面类型的SEO配置
        
        Args:
            page_type (str): 页面类型
            
        Returns:
            Dict[str, Any]: SEO配置
        """
        return cls.SEO_CONFIG.get(page_type, cls.SEO_CONFIG['quote_detail'])
    
    @classmethod
    def get_url_for_type(cls, url_type: str, slug: str = None, id: int = None) -> str:
        """
        根据类型生成URL
        
        Args:
            url_type (str): URL类型
            slug (str): slug（可选）
            id (int): ID（可选）
            
        Returns:
            str: 生成的URL
        """
        base_path = cls.PATHS.get(url_type.upper())
        if not base_path:
            raise ValueError(f"未知的URL类型: {url_type}")
        
        if slug:
            return f"{cls.BASE_URL}/{base_path}/{slug}/"
        elif id:
            return f"{cls.BASE_URL}/{base_path}/{id}/"
        else:
            return f"{cls.BASE_URL}/{base_path}/"
    
    @classmethod
    def is_url_type_enabled(cls, url_type: str) -> bool:
        """
        检查URL类型是否启用

        Args:
            url_type (str): URL类型名称

        Returns:
            bool: 是否启用
        """
        return cls.URL_TYPES_ENABLED.get(url_type, False)

    @classmethod
    def get_enabled_url_types(cls) -> list:
        """
        获取所有启用的URL类型

        Returns:
            list: 启用的URL类型列表
        """
        return [url_type for url_type, enabled in cls.URL_TYPES_ENABLED.items() if enabled]

    @classmethod
    def get_disabled_url_types(cls) -> list:
        """
        获取所有禁用的URL类型

        Returns:
            list: 禁用的URL类型列表
        """
        return [url_type for url_type, enabled in cls.URL_TYPES_ENABLED.items() if not enabled]

    @classmethod
    def get_url_type_description(cls, url_type: str) -> str:
        """
        获取URL类型的描述

        Args:
            url_type (str): URL类型名称

        Returns:
            str: URL类型描述
        """
        return cls.URL_TYPES_DESCRIPTION.get(url_type, f"未知URL类型: {url_type}")

    @classmethod
    def is_enabled_search_engine(cls, engine: str) -> bool:
        """
        检查搜索引擎是否启用

        Args:
            engine (str): 搜索引擎名称

        Returns:
            bool: 是否启用
        """
        engine_config = cls.SEARCH_ENGINES.get(engine)
        return engine_config and engine_config.get('enabled', False)
    
    @classmethod
    def get_output_path(cls, absolute: bool = False) -> str:
        """
        获取输出路径
        
        Args:
            absolute (bool): 是否返回绝对路径
            
        Returns:
            str: 输出路径
        """
        if absolute:
            return os.path.abspath(cls.OUTPUT_PATH)
        return cls.OUTPUT_PATH
    
    @classmethod
    def validate_config(cls) -> bool:
        """
        验证配置的有效性
        
        Returns:
            bool: 配置是否有效
        """
        try:
            # 验证基础URL
            if not cls.BASE_URL.startswith(('http://', 'https://')):
                return False
            
            # 验证路径配置
            if not all(isinstance(path, str) for path in cls.PATHS.values()):
                return False
            
            # 验证SEO配置
            for config in cls.SEO_CONFIG.values():
                if not all(key in config for key in ['priority', 'changefreq']):
                    return False
                
                priority = float(config['priority'])
                if not 0.0 <= priority <= 1.0:
                    return False
            
            return True
            
        except (ValueError, TypeError):
            return False


# 创建默认配置实例
default_config = SitemapConfig()

# 验证配置
if not default_config.validate_config():
    raise ValueError("Sitemap配置验证失败，请检查配置参数")
