#!/usr/bin/env python
"""
Quotese网站地图生成脚本 v2.0
支持新的语义化URL架构和SEO优化
此脚本连接到数据库，获取所有名言、作者、类别和来源的信息，
然后生成一个完整的sitemap.xml文件，符合搜索引擎优化最佳实践。

版本：v2.0 - SEO重启实施
更新日期：2025年6月16日
"""

import os
import sys
import django
import datetime
import re
import logging
from typing import List, Dict, Optional
from xml.sax.saxutils import escape

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'quotes_admin.settings')
django.setup()

# 导入模型
from quotesapp.models import Quotes, Authors, Categories, Sources

# 导入配置
from sitemap_config import SitemapConfig

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('sitemap_generation.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


# 注意：SitemapConfig类现在从sitemap_config.py导入


def slugify_custom(text: str) -> str:
    """
    自定义slugify函数，与前端JavaScript版本保持完全一致
    遵循UrlHandler.slugify的逻辑

    Args:
        text (str): 要转换的文本

    Returns:
        str: URL友好的slug
    """
    if not text:
        return ''

    # 转换为字符串并处理
    text = str(text)

    # 转换为小写
    text = text.lower()

    # 去除首尾空格
    text = text.strip()

    # 空格替换为连字符
    text = re.sub(r'\s+', '-', text)

    # 删除非单词字符（保留字母、数字、连字符）
    text = re.sub(r'[^\w\-]+', '', text)

    # 多个连字符合并为一个
    text = re.sub(r'\-\-+', '-', text)

    # 删除开头连字符
    text = re.sub(r'^-+', '', text)

    # 删除结尾连字符
    text = re.sub(r'-+$', '', text)

    return text


def is_valid_slug(slug: str) -> bool:
    """
    验证slug格式是否正确

    Args:
        slug (str): 要验证的slug

    Returns:
        bool: 是否为有效的slug
    """
    if not slug or not isinstance(slug, str):
        return False

    # slug应该只包含小写字母、数字和连字符，不能以连字符开头或结尾
    return bool(re.match(r'^[a-z0-9]+(-[a-z0-9]+)*$', slug))

class SitemapGenerator:
    """Sitemap生成器类"""

    def __init__(self, config: SitemapConfig = None):
        self.config = config or SitemapConfig()
        self.urls = []

    def add_url(self, loc: str, lastmod: str = None, changefreq: str = None, priority: str = None):
        """
        添加URL到sitemap

        Args:
            loc (str): URL地址
            lastmod (str): 最后修改时间
            changefreq (str): 更新频率
            priority (str): 优先级
        """
        url_data = {
            'loc': escape(loc),
            'lastmod': lastmod or datetime.datetime.now().strftime("%Y-%m-%d"),
            'changefreq': changefreq or 'weekly',
            'priority': priority or '0.5'
        }
        self.urls.append(url_data)

    def generate_home_page(self):
        """生成首页URL"""
        if not self.config.is_url_type_enabled('home'):
            logger.info("首页URL已禁用，跳过生成")
            return

        logger.info("生成首页URL...")
        seo_config = self.config.SEO_CONFIG['home']

        self.add_url(
            loc=f"{self.config.BASE_URL}/",
            changefreq=seo_config['changefreq'],
            priority=seo_config['priority']
        )

    def generate_list_pages(self):
        """生成列表页面URL"""
        logger.info("检查列表页面URL配置...")
        seo_config = self.config.SEO_CONFIG['list_pages']

        # 检查各个列表页面的启用状态
        list_page_configs = [
            ('authors_list', f"{self.config.BASE_URL}/{self.config.PATHS['AUTHORS']}/", "作者列表页"),
            ('categories_list', f"{self.config.BASE_URL}/{self.config.PATHS['CATEGORIES']}/", "类别列表页"),
            ('sources_list', f"{self.config.BASE_URL}/{self.config.PATHS['SOURCES']}/", "来源列表页"),
            ('quotes_list', f"{self.config.BASE_URL}/{self.config.PATHS['QUOTES']}/", "名言列表页")
        ]

        generated_count = 0
        for url_type, url, description in list_page_configs:
            if self.config.is_url_type_enabled(url_type):
                logger.info(f"生成{description}: {url}")
                self.add_url(
                    loc=url,
                    changefreq=seo_config['changefreq'],
                    priority=seo_config['priority']
                )
                generated_count += 1
            else:
                logger.info(f"跳过{description}（已禁用）")

        logger.info(f"列表页面生成完成，共生成 {generated_count} 个URL")

    def generate_author_pages(self):
        """生成作者页面URL"""
        logger.info("检查作者页面URL配置...")

        # 检查作者详情页是否启用
        author_detail_enabled = self.config.is_url_type_enabled('author_detail')
        author_quotes_enabled = self.config.is_url_type_enabled('author_quotes')

        if not author_detail_enabled and not author_quotes_enabled:
            logger.info("作者相关页面均已禁用，跳过生成")
            return

        seo_config = self.config.SEO_CONFIG['author_detail']

        try:
            authors = Authors.objects.all()
            logger.info(f"找到 {len(authors)} 个作者")

            detail_count = 0
            quotes_count = 0

            for author in authors:
                slug = slugify_custom(author.name)

                if not slug or not is_valid_slug(slug):
                    logger.warning(f"跳过无效slug的作者: {author.name}")
                    continue

                # 作者详情页
                if author_detail_enabled:
                    author_url = f"{self.config.BASE_URL}/{self.config.PATHS['AUTHORS']}/{slug}/"
                    self.add_url(
                        loc=author_url,
                        changefreq=seo_config['changefreq'],
                        priority=seo_config['priority']
                    )
                    detail_count += 1

                # 作者名言列表页
                if author_quotes_enabled:
                    author_quotes_url = f"{self.config.BASE_URL}/{self.config.PATHS['AUTHORS']}/{slug}/quotes/"
                    self.add_url(
                        loc=author_quotes_url,
                        changefreq=seo_config['changefreq'],
                        priority=str(float(seo_config['priority']) - 0.1)  # 稍低优先级
                    )
                    quotes_count += 1

            logger.info(f"作者页面生成完成 - 详情页: {detail_count}, 名言页: {quotes_count}")

        except Exception as e:
            logger.error(f"生成作者页面URL时出错: {e}")

    def generate_category_pages(self):
        """生成类别页面URL"""
        logger.info("检查类别页面URL配置...")

        # 检查类别详情页是否启用
        category_detail_enabled = self.config.is_url_type_enabled('category_detail')
        category_quotes_enabled = self.config.is_url_type_enabled('category_quotes')

        if not category_detail_enabled and not category_quotes_enabled:
            logger.info("类别相关页面均已禁用，跳过生成")
            return

        seo_config = self.config.SEO_CONFIG['category_detail']

        try:
            categories = Categories.objects.all()
            logger.info(f"找到 {len(categories)} 个类别")

            detail_count = 0
            quotes_count = 0

            for category in categories:
                slug = slugify_custom(category.name)

                if not slug or not is_valid_slug(slug):
                    logger.warning(f"跳过无效slug的类别: {category.name}")
                    continue

                # 类别详情页
                if category_detail_enabled:
                    category_url = f"{self.config.BASE_URL}/{self.config.PATHS['CATEGORIES']}/{slug}/"
                    self.add_url(
                        loc=category_url,
                        changefreq=seo_config['changefreq'],
                        priority=seo_config['priority']
                    )
                    detail_count += 1

                # 类别名言列表页
                if category_quotes_enabled:
                    category_quotes_url = f"{self.config.BASE_URL}/{self.config.PATHS['CATEGORIES']}/{slug}/quotes/"
                    self.add_url(
                        loc=category_quotes_url,
                        changefreq=seo_config['changefreq'],
                        priority=str(float(seo_config['priority']) - 0.1)  # 稍低优先级
                    )
                    quotes_count += 1

            logger.info(f"类别页面生成完成 - 详情页: {detail_count}, 名言页: {quotes_count}")

        except Exception as e:
            logger.error(f"生成类别页面URL时出错: {e}")

    def generate_source_pages(self):
        """生成来源页面URL"""
        if not self.config.is_url_type_enabled('source_detail'):
            logger.info("来源详情页已禁用，跳过生成")
            return

        logger.info("生成来源页面URL...")
        seo_config = self.config.SEO_CONFIG['source_detail']

        try:
            sources = Sources.objects.all()
            logger.info(f"找到 {len(sources)} 个来源")

            detail_count = 0

            for source in sources:
                slug = slugify_custom(source.name)

                if not slug or not is_valid_slug(slug):
                    logger.warning(f"跳过无效slug的来源: {source.name}")
                    continue

                # 来源详情页
                source_url = f"{self.config.BASE_URL}/{self.config.PATHS['SOURCES']}/{slug}/"
                self.add_url(
                    loc=source_url,
                    changefreq=seo_config['changefreq'],
                    priority=seo_config['priority']
                )
                detail_count += 1

            logger.info(f"来源页面生成完成 - 详情页: {detail_count}")

        except Exception as e:
            logger.error(f"生成来源页面URL时出错: {e}")

    def generate_quote_pages(self):
        """生成名言页面URL"""
        if not self.config.is_url_type_enabled('quote_detail'):
            logger.info("名言详情页已禁用，跳过生成")
            return

        logger.info("生成名言页面URL...")
        seo_config = self.config.SEO_CONFIG['quote_detail']

        try:
            # 限制名言数量，避免sitemap过大
            quotes = Quotes.objects.all()[:self.config.MAX_QUOTES]
            logger.info(f"找到 {Quotes.objects.count()} 个名言，生成前 {len(quotes)} 个")

            detail_count = 0

            for quote in quotes:
                if not quote.id or quote.id <= 0:
                    logger.warning(f"跳过无效ID的名言: {quote.id}")
                    continue

                # 名言详情页
                quote_url = f"{self.config.BASE_URL}/{self.config.PATHS['QUOTES']}/{quote.id}/"
                self.add_url(
                    loc=quote_url,
                    changefreq=seo_config['changefreq'],
                    priority=seo_config['priority']
                )
                detail_count += 1

            logger.info(f"名言页面生成完成 - 详情页: {detail_count}")

        except Exception as e:
            logger.error(f"生成名言页面URL时出错: {e}")

    def write_xml_file(self, output_path: str = None):
        """
        将URL列表写入XML文件

        Args:
            output_path (str): 输出文件路径
        """
        output_path = output_path or self.config.OUTPUT_PATH

        try:
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            with open(output_path, 'w', encoding='utf-8') as f:
                # XML头部
                f.write('<?xml version="1.0" encoding="UTF-8"?>\n')
                f.write('<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n')

                # 写入所有URL
                for url_data in self.urls:
                    f.write('  <url>\n')
                    f.write(f'    <loc>{url_data["loc"]}</loc>\n')
                    f.write(f'    <lastmod>{url_data["lastmod"]}</lastmod>\n')
                    f.write(f'    <changefreq>{url_data["changefreq"]}</changefreq>\n')
                    f.write(f'    <priority>{url_data["priority"]}</priority>\n')
                    f.write('  </url>\n')

                # XML尾部
                f.write('</urlset>\n')

            logger.info(f"Sitemap已生成: {output_path}")
            logger.info(f"总共包含 {len(self.urls)} 个URL")

        except Exception as e:
            logger.error(f"写入XML文件时出错: {e}")
            raise

    def generate_sitemap(self, output_path: str = None):
        """
        生成完整的sitemap

        Args:
            output_path (str): 输出文件路径
        """
        logger.info("开始生成sitemap...")

        # 输出当前配置状态
        enabled_types = self.config.get_enabled_url_types()
        disabled_types = self.config.get_disabled_url_types()

        logger.info(f"启用的URL类型 ({len(enabled_types)}): {', '.join(enabled_types)}")
        logger.info(f"禁用的URL类型 ({len(disabled_types)}): {', '.join(disabled_types)}")

        # 清空URL列表
        self.urls = []

        # 生成各类页面URL
        self.generate_home_page()
        self.generate_list_pages()
        self.generate_author_pages()
        self.generate_category_pages()
        self.generate_source_pages()
        self.generate_quote_pages()

        # 写入XML文件
        self.write_xml_file(output_path)

        logger.info("Sitemap生成完成!")

        return len(self.urls)

def main():
    """主函数"""
    try:
        # 创建sitemap生成器
        generator = SitemapGenerator()

        # 生成sitemap
        url_count = generator.generate_sitemap()

        # 输出统计信息
        print(f"\n✅ Sitemap生成成功!")
        print(f"📊 统计信息:")
        print(f"   - 总URL数量: {url_count}")
        print(f"   - 输出文件: {generator.config.OUTPUT_PATH}")
        print(f"   - 网站地址: {generator.config.BASE_URL}")

        # 验证生成的文件
        if os.path.exists(generator.config.OUTPUT_PATH):
            file_size = os.path.getsize(generator.config.OUTPUT_PATH)
            print(f"   - 文件大小: {file_size:,} 字节")

        return True

    except Exception as e:
        logger.error(f"生成sitemap失败: {e}")
        print(f"\n❌ 生成sitemap失败: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
