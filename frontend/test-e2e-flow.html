<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>End-to-End Quote Detail Flow Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .quote-card-demo {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease, border-color 0.2s ease;
            background: white;
        }
        .quote-card-demo:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
            border-color: #ffd300;
        }
        .quote-content {
            font-size: 18px;
            font-style: italic;
            margin-bottom: 15px;
            line-height: 1.6;
        }
        .quote-author {
            font-weight: bold;
            color: #666;
            margin-bottom: 10px;
        }
        .quote-meta {
            font-size: 12px;
            color: #999;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button.success {
            background: #28a745;
        }
        .test-button.danger {
            background: #dc3545;
        }
        .test-result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .loading {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .hero-card-demo {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            margin: 20px 0;
        }
        .hero-card-demo:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.2);
        }
        .hero-quote-content {
            font-size: 24px;
            font-style: italic;
            margin-bottom: 15px;
            line-height: 1.4;
        }
        .hero-quote-author {
            font-size: 18px;
            font-weight: bold;
            opacity: 0.9;
        }
        .flow-step {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 10px 0;
            border-radius: 0 4px 4px 0;
        }
        .flow-step.active {
            background: #e3f2fd;
            border-left-color: #2196f3;
        }
        .flow-step.success {
            background: #e8f5e8;
            border-left-color: #4caf50;
        }
        .flow-step.error {
            background: #ffebee;
            border-left-color: #f44336;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">End-to-End Quote Detail Flow Test</h1>
        <p>This test simulates the complete user journey from clicking a quote card to viewing the detail page.</p>
        
        <div style="display: flex; gap: 10px; margin-bottom: 20px;">
            <button class="test-button" onclick="runCompleteFlow()">🚀 Run Complete Flow Test</button>
            <button class="test-button" onclick="testIndividualSteps()">🔍 Test Individual Steps</button>
            <button class="test-button" onclick="clearResults()">🧹 Clear Results</button>
        </div>

        <div id="flow-status" class="test-result info">
            Ready to test the complete quote detail flow...
        </div>
    </div>

    <!-- Flow Steps Visualization -->
    <div class="test-container">
        <h2 class="test-title">Flow Steps</h2>
        
        <div id="step1" class="flow-step">
            <strong>Step 1:</strong> Load quote data from API
            <div id="step1-details" style="margin-top: 5px; font-size: 12px; color: #666;"></div>
        </div>
        
        <div id="step2" class="flow-step">
            <strong>Step 2:</strong> Generate quote detail URL
            <div id="step2-details" style="margin-top: 5px; font-size: 12px; color: #666;"></div>
        </div>
        
        <div id="step3" class="flow-step">
            <strong>Step 3:</strong> Simulate quote card click
            <div id="step3-details" style="margin-top: 5px; font-size: 12px; color: #666;"></div>
        </div>
        
        <div id="step4" class="flow-step">
            <strong>Step 4:</strong> Verify navigation would occur
            <div id="step4-details" style="margin-top: 5px; font-size: 12px; color: #666;"></div>
        </div>
        
        <div id="step5" class="flow-step">
            <strong>Step 5:</strong> Test quote detail page loading
            <div id="step5-details" style="margin-top: 5px; font-size: 12px; color: #666;"></div>
        </div>
    </div>

    <!-- Interactive Demo Cards -->
    <div class="test-container">
        <h2 class="test-title">Interactive Demo Cards</h2>
        <p>Click these cards to test the actual navigation flow:</p>
        
        <!-- Hero Card Demo -->
        <div class="hero-card-demo" id="hero-demo" onclick="testHeroCardFlow()">
            <div class="hero-quote-content">"The greatest glory in living lies not in never falling, but in rising every time we fall."</div>
            <div class="hero-quote-author">— Nelson Mandela</div>
            <div style="margin-top: 15px; font-size: 14px; opacity: 0.8;">
                🎯 Hero Quote Card - Click to test navigation
            </div>
        </div>

        <!-- Regular Quote Cards Demo -->
        <div class="quote-card-demo" onclick="testQuoteCardFlow(1)">
            <div class="quote-content">"The way to get started is to quit talking and begin doing."</div>
            <div class="quote-author">— Walt Disney</div>
            <div class="quote-meta">
                <span>💡 Regular Quote Card</span>
                <span>ID: 1</span>
            </div>
        </div>

        <div class="quote-card-demo" onclick="testQuoteCardFlow(2)">
            <div class="quote-content">"Innovation distinguishes between a leader and a follower."</div>
            <div class="quote-author">— Steve Jobs</div>
            <div class="quote-meta">
                <span>💡 Regular Quote Card</span>
                <span>ID: 2</span>
            </div>
        </div>

        <div class="quote-card-demo" onclick="testQuoteCardFlow(3)">
            <div class="quote-content">"Life is what happens to you while you're busy making other plans."</div>
            <div class="quote-author">— John Lennon</div>
            <div class="quote-meta">
                <span>💡 Regular Quote Card</span>
                <span>ID: 3</span>
            </div>
        </div>

        <div id="demo-results" class="test-result"></div>
    </div>

    <!-- Load required scripts -->
    <script src="js/config.js"></script>
    <script src="js/mock-data.js"></script>
    <script src="js/api-client.js"></script>
    <script src="js/url-handler.js"></script>

    <script>
        let flowResults = {
            steps: [],
            overall: { passed: 0, failed: 0, total: 5 }
        };

        // Utility functions
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = message;
                element.className = `test-result ${type}`;
            }
        }

        function updateStepStatus(stepNumber, status, details = '') {
            const stepElement = document.getElementById(`step${stepNumber}`);
            const detailsElement = document.getElementById(`step${stepNumber}-details`);
            
            if (stepElement) {
                stepElement.className = `flow-step ${status}`;
            }
            
            if (detailsElement && details) {
                detailsElement.textContent = details;
            }
        }

        function recordStepResult(stepNumber, passed, details = '') {
            flowResults.steps[stepNumber - 1] = { passed, details };
            if (passed) {
                flowResults.overall.passed++;
                updateStepStatus(stepNumber, 'success', `✅ ${details}`);
            } else {
                flowResults.overall.failed++;
                updateStepStatus(stepNumber, 'error', `❌ ${details}`);
            }
            updateOverallStatus();
        }

        function updateOverallStatus() {
            const { passed, failed, total } = flowResults.overall;
            const percentage = total > 0 ? Math.round((passed / total) * 100) : 0;
            const status = failed === 0 ? 'success' : (passed > failed ? 'info' : 'error');
            
            showResult('flow-status', 
                `Flow Test Results: ${passed}/${total} steps passed (${percentage}%)\n` +
                `✅ Passed: ${passed}\n` +
                `❌ Failed: ${failed}\n` +
                `Status: ${failed === 0 ? 'All steps completed successfully!' : 'Some steps failed'}`,
                status
            );
        }

        // Main flow test
        async function runCompleteFlow() {
            showResult('flow-status', 'Running complete end-to-end flow test...', 'loading');
            
            // Reset results
            flowResults = {
                steps: [],
                overall: { passed: 0, failed: 0, total: 5 }
            };

            // Reset all steps
            for (let i = 1; i <= 5; i++) {
                updateStepStatus(i, '', 'Waiting...');
            }

            try {
                // Step 1: Load quote data from API
                updateStepStatus(1, 'active', 'Loading quote data...');
                const quote = await window.ApiClient.getQuote(1);
                
                if (quote && quote.id && quote.content && quote.author) {
                    recordStepResult(1, true, `Loaded quote: "${quote.content.substring(0, 50)}..." by ${quote.author.name}`);
                } else {
                    recordStepResult(1, false, 'Failed to load valid quote data');
                    return;
                }

                // Step 2: Generate quote detail URL
                updateStepStatus(2, 'active', 'Generating URL...');
                const url = UrlHandler.getQuoteUrl(quote);
                
                if (url && url.includes(`/quotes/${quote.id}/`)) {
                    recordStepResult(2, true, `Generated URL: ${url}`);
                } else {
                    recordStepResult(2, false, `Invalid URL generated: ${url}`);
                    return;
                }

                // Step 3: Simulate quote card click
                updateStepStatus(3, 'active', 'Simulating card click...');
                const clickEvent = simulateQuoteCardClick(quote);
                
                if (clickEvent.success) {
                    recordStepResult(3, true, `Click simulation successful: ${clickEvent.details}`);
                } else {
                    recordStepResult(3, false, `Click simulation failed: ${clickEvent.details}`);
                    return;
                }

                // Step 4: Verify navigation would occur
                updateStepStatus(4, 'active', 'Verifying navigation...');
                const navigationTest = verifyNavigation(url);
                
                if (navigationTest.success) {
                    recordStepResult(4, true, `Navigation verification passed: ${navigationTest.details}`);
                } else {
                    recordStepResult(4, false, `Navigation verification failed: ${navigationTest.details}`);
                    return;
                }

                // Step 5: Test quote detail page loading
                updateStepStatus(5, 'active', 'Testing detail page loading...');
                const pageLoadTest = await testQuoteDetailPageLoad(quote.id);
                
                if (pageLoadTest.success) {
                    recordStepResult(5, true, `Detail page test passed: ${pageLoadTest.details}`);
                } else {
                    recordStepResult(5, false, `Detail page test failed: ${pageLoadTest.details}`);
                }

            } catch (error) {
                showResult('flow-status', `Flow test ERROR: ${error.message}`, 'error');
                console.error('Flow test error:', error);
            }
        }

        // Individual test functions
        function simulateQuoteCardClick(quote) {
            try {
                // Create a mock quote card element
                const mockCard = document.createElement('div');
                mockCard.className = 'quote-card-component cursor-pointer';
                mockCard.setAttribute('data-quote-id', quote.id);
                
                // Simulate the click event handler logic
                const url = UrlHandler.getQuoteUrl(quote);
                
                // Check if the URL would be valid for navigation
                if (url && url.includes(`/quotes/${quote.id}/`)) {
                    return {
                        success: true,
                        details: `Would navigate to: ${url}`
                    };
                } else {
                    return {
                        success: false,
                        details: `Invalid navigation URL: ${url}`
                    };
                }
            } catch (error) {
                return {
                    success: false,
                    details: error.message
                };
            }
        }

        function verifyNavigation(url) {
            try {
                // Verify URL structure
                const urlPattern = /\/quotes\/\d+\//;
                if (!urlPattern.test(url)) {
                    return {
                        success: false,
                        details: 'URL does not match expected pattern /quotes/{id}/'
                    };
                }

                // Verify URL is accessible (basic check)
                try {
                    const urlObj = new URL(url, window.location.origin);
                    return {
                        success: true,
                        details: `Valid URL structure: ${urlObj.pathname}`
                    };
                } catch (urlError) {
                    return {
                        success: false,
                        details: `Invalid URL format: ${urlError.message}`
                    };
                }
            } catch (error) {
                return {
                    success: false,
                    details: error.message
                };
            }
        }

        async function testQuoteDetailPageLoad(quoteId) {
            try {
                // Test if we can load the quote data that would be needed for the detail page
                const quote = await window.ApiClient.getQuote(quoteId);
                
                if (!quote) {
                    return {
                        success: false,
                        details: 'Quote data not available for detail page'
                    };
                }

                // Verify all required data is present for detail page
                const requiredFields = ['id', 'content', 'author'];
                const missingFields = requiredFields.filter(field => !quote[field]);
                
                if (missingFields.length > 0) {
                    return {
                        success: false,
                        details: `Missing required fields: ${missingFields.join(', ')}`
                    };
                }

                // Test URL generation for detail page
                const detailUrl = UrlHandler.getQuoteUrl(quote);
                if (!detailUrl) {
                    return {
                        success: false,
                        details: 'Could not generate detail page URL'
                    };
                }

                return {
                    success: true,
                    details: `All data available for detail page: ${quote.content.substring(0, 30)}...`
                };
            } catch (error) {
                return {
                    success: false,
                    details: error.message
                };
            }
        }

        // Individual step testing
        async function testIndividualSteps() {
            showResult('flow-status', 'Testing individual steps...', 'loading');
            
            const tests = [
                { name: 'API Data Loading', func: () => testAPIDataLoading() },
                { name: 'URL Generation', func: () => testURLGeneration() },
                { name: 'Click Simulation', func: () => testClickSimulation() },
                { name: 'Navigation Logic', func: () => testNavigationLogic() },
                { name: 'Page Load Preparation', func: () => testPageLoadPreparation() }
            ];

            let results = [];
            for (let i = 0; i < tests.length; i++) {
                const test = tests[i];
                try {
                    updateStepStatus(i + 1, 'active', `Testing ${test.name}...`);
                    const result = await test.func();
                    results.push(`✅ ${test.name}: ${result}`);
                    updateStepStatus(i + 1, 'success', result);
                } catch (error) {
                    results.push(`❌ ${test.name}: ${error.message}`);
                    updateStepStatus(i + 1, 'error', error.message);
                }
            }

            showResult('flow-status', 
                `Individual step testing completed:\n\n${results.join('\n')}`, 
                results.every(r => r.startsWith('✅')) ? 'success' : 'error'
            );
        }

        async function testAPIDataLoading() {
            const quote = await window.ApiClient.getQuote(1);
            if (!quote || !quote.id || !quote.content) {
                throw new Error('API did not return valid quote data');
            }
            return `Loaded quote ${quote.id}: "${quote.content.substring(0, 30)}..."`;
        }

        function testURLGeneration() {
            const testQuote = { id: 1, content: "Test quote" };
            const url = UrlHandler.getQuoteUrl(testQuote);
            if (!url || !url.includes('/quotes/1/')) {
                throw new Error('URL generation failed or returned invalid URL');
            }
            return `Generated valid URL: ${url}`;
        }

        function testClickSimulation() {
            const testQuote = { id: 1, content: "Test quote" };
            const result = simulateQuoteCardClick(testQuote);
            if (!result.success) {
                throw new Error(result.details);
            }
            return result.details;
        }

        function testNavigationLogic() {
            const testUrl = '/quotes/1/';
            const result = verifyNavigation(testUrl);
            if (!result.success) {
                throw new Error(result.details);
            }
            return result.details;
        }

        async function testPageLoadPreparation() {
            const result = await testQuoteDetailPageLoad(1);
            if (!result.success) {
                throw new Error(result.details);
            }
            return result.details;
        }

        // Demo card interactions
        function testQuoteCardFlow(quoteId) {
            showResult('demo-results', `Testing quote card flow for ID: ${quoteId}...`, 'loading');
            
            try {
                const testQuote = { id: quoteId, content: `Test quote ${quoteId}` };
                const url = UrlHandler.getQuoteUrl(testQuote);
                
                showResult('demo-results', 
                    `✅ Quote Card Flow Test SUCCESS:\n` +
                    `Quote ID: ${quoteId}\n` +
                    `Generated URL: ${url}\n` +
                    `Click would navigate to: ${url}\n\n` +
                    `To actually test navigation, click the button below:`, 'success');
                
                // Add a button to actually navigate
                const demoElement = document.getElementById('demo-results');
                const navButton = document.createElement('button');
                navButton.textContent = `🔗 Navigate to Quote ${quoteId} Detail Page`;
                navButton.className = 'test-button success';
                navButton.onclick = () => window.open(url, '_blank');
                demoElement.appendChild(document.createElement('br'));
                demoElement.appendChild(navButton);
                
            } catch (error) {
                showResult('demo-results', `❌ Quote card flow ERROR: ${error.message}`, 'error');
            }
        }

        function testHeroCardFlow() {
            showResult('demo-results', 'Testing hero card flow...', 'loading');
            
            try {
                // Simulate hero card with quote ID
                const heroQuoteId = 1;
                const testQuote = { id: heroQuoteId, content: "Hero quote content" };
                const url = UrlHandler.getQuoteUrl(testQuote);
                
                showResult('demo-results', 
                    `✅ Hero Card Flow Test SUCCESS:\n` +
                    `Hero Quote ID: ${heroQuoteId}\n` +
                    `Generated URL: ${url}\n` +
                    `Hero card click would navigate to: ${url}\n\n` +
                    `To actually test navigation, click the button below:`, 'success');
                
                // Add a button to actually navigate
                const demoElement = document.getElementById('demo-results');
                const navButton = document.createElement('button');
                navButton.textContent = `🔗 Navigate to Hero Quote Detail Page`;
                navButton.className = 'test-button success';
                navButton.onclick = () => window.open(url, '_blank');
                demoElement.appendChild(document.createElement('br'));
                demoElement.appendChild(navButton);
                
            } catch (error) {
                showResult('demo-results', `❌ Hero card flow ERROR: ${error.message}`, 'error');
            }
        }

        function clearResults() {
            showResult('flow-status', 'Results cleared. Ready to test...', 'info');
            showResult('demo-results', '', 'info');
            
            // Reset all steps
            for (let i = 1; i <= 5; i++) {
                updateStepStatus(i, '', 'Ready to test...');
            }
            
            flowResults = {
                steps: [],
                overall: { passed: 0, failed: 0, total: 5 }
            };
        }

        // Initialize page
        window.addEventListener('load', function() {
            console.log('E2E Flow Test Suite loaded');
            console.log('Available objects:', {
                ApiClient: !!window.ApiClient,
                UrlHandler: !!window.UrlHandler,
                getQuote: typeof window.ApiClient?.getQuote
            });
            
            showResult('flow-status', 
                'End-to-End Flow Test Suite ready!\n' +
                `ApiClient available: ${!!window.ApiClient}\n` +
                `UrlHandler available: ${!!window.UrlHandler}\n` +
                `getQuote method: ${typeof window.ApiClient?.getQuote}\n\n` +
                'Click "Run Complete Flow Test" to start testing.', 'info');
        });
    </script>
</body>
</html>
