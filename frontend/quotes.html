<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All Famous Quotes | Quote Collection - Quotese.com</title>
    <meta name="description" content="Browse our complete collection of famous quotes. Find inspiration and wisdom from great minds.">
    <meta name="keywords" content="famous quotes, all quotes, quote collection, inspirational quotes, wisdom, motivation">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/css/variables.css">
    <link rel="stylesheet" href="/css/styles.css">
    <link rel="stylesheet" href="/css/responsive.css">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <div id="navigation-container"></div>
    
    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto">
            <!-- Page Header -->
            <div class="text-center mb-12">
                <h1 class="text-4xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-quote-left mr-3 text-red-500"></i>
                    Famous Quotes Collection
                </h1>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Explore our vast collection of inspiring quotes from history's greatest minds and voices.
                </p>
            </div>
            
            <!-- Search and Filter Bar -->
            <div class="mb-8">
                <div class="max-w-4xl mx-auto">
                    <div class="flex flex-col md:flex-row gap-4">
                        <!-- Search Input -->
                        <div class="flex-1 relative">
                            <input type="text" id="quote-search" placeholder="Search quotes..." 
                                   class="w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent">
                            <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                        </div>
                        
                        <!-- Filter Dropdown -->
                        <div class="relative">
                            <select id="quote-filter" class="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent">
                                <option value="">All Categories</option>
                                <option value="inspirational">Inspirational</option>
                                <option value="motivational">Motivational</option>
                                <option value="life">Life</option>
                                <option value="success">Success</option>
                                <option value="wisdom">Wisdom</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quotes Grid -->
            <div id="quotes-grid" class="space-y-6">
                <!-- Quotes will be loaded here -->
            </div>
            
            <!-- Pagination -->
            <div id="pagination-container" class="mt-12"></div>
        </div>
    </main>
    
    <!-- Footer -->
    <div id="footer-container"></div>
    
    <!-- Core Scripts -->
    <script src="/js/theme.js"></script>
    <script src="/js/url-handler.js"></script>
    <script src="/js/seo-manager.js"></script>
    <script src="/js/page-router.js"></script>
    <script src="/js/mobile-menu.js"></script>
    <script src="/js/components/pagination.js"></script>
    <script src="/js/components/quote-card.js"></script>
    <script src="/js/components/breadcrumb.js"></script>
    <script src="/js/social-meta.js"></script>

    <!-- Page Scripts -->
    <script src="/js/pages/quotes.js"></script>

    <!-- Component Loader -->
    <script src="/js/component-loader.js"></script>
</body>
</html>
