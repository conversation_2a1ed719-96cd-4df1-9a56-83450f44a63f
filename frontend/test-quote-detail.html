<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quote Detail Functionality Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button.success {
            background: #28a745;
        }
        .test-button.danger {
            background: #dc3545;
        }
        .test-result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .loading {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .quote-card-demo {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        .quote-card-demo:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .quote-content {
            font-size: 18px;
            font-style: italic;
            margin-bottom: 10px;
        }
        .quote-author {
            font-weight: bold;
            color: #666;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
        .status-info { background: #17a2b8; }
    </style>
</head>
<body>
    <div class="test-section">
        <h1 class="test-title">Quote Detail Functionality Test Suite</h1>
        <p>This comprehensive test verifies all aspects of the quote detail functionality.</p>
        
        <div style="display: flex; gap: 10px; margin-bottom: 20px;">
            <button class="test-button" onclick="runAllTests()">🚀 Run All Tests</button>
            <button class="test-button" onclick="clearResults()">🧹 Clear Results</button>
            <button class="test-button" onclick="openQuoteDetail(1)">🔗 Test Quote Detail Page</button>
        </div>

        <div id="overall-status" class="test-result info">
            Ready to run tests...
        </div>
    </div>

    <!-- API Tests -->
    <div class="test-section">
        <h2 class="test-title">1. API Method Tests</h2>
        
        <div style="margin-bottom: 15px;">
            <button class="test-button" onclick="testGetQuoteAPI()">Test getQuote API</button>
            <button class="test-button" onclick="testAPIErrorHandling()">Test Error Handling</button>
            <button class="test-button" onclick="testAPICaching()">Test Caching</button>
        </div>
        
        <div id="api-test-results" class="test-result"></div>
    </div>

    <!-- Component Tests -->
    <div class="test-section">
        <h2 class="test-title">2. Component Functionality Tests</h2>
        
        <div style="margin-bottom: 15px;">
            <button class="test-button" onclick="testQuoteCardClick()">Test Quote Card Click</button>
            <button class="test-button" onclick="testHeroCardClick()">Test Hero Card Click</button>
            <button class="test-button" onclick="testURLGeneration()">Test URL Generation</button>
        </div>
        
        <div id="component-test-results" class="test-result"></div>
    </div>

    <!-- Integration Tests -->
    <div class="test-section">
        <h2 class="test-title">3. Integration Tests</h2>
        
        <div style="margin-bottom: 15px;">
            <button class="test-button" onclick="testEndToEndFlow()">Test End-to-End Flow</button>
            <button class="test-button" onclick="testPageNavigation()">Test Page Navigation</button>
            <button class="test-button" onclick="testDataFlow()">Test Data Flow</button>
        </div>
        
        <div id="integration-test-results" class="test-result"></div>
    </div>

    <!-- Demo Quote Cards -->
    <div class="test-section">
        <h2 class="test-title">4. Interactive Demo</h2>
        <p>Click on these demo quote cards to test the functionality:</p>
        
        <div class="quote-card-demo" onclick="testQuoteCardNavigation(1)">
            <div class="quote-content">"The greatest glory in living lies not in never falling, but in rising every time we fall."</div>
            <div class="quote-author">— Nelson Mandela</div>
            <small style="color: #999;">Click to test navigation to quote detail page</small>
        </div>
        
        <div class="quote-card-demo" onclick="testQuoteCardNavigation(2)">
            <div class="quote-content">"The way to get started is to quit talking and begin doing."</div>
            <div class="quote-author">— Walt Disney</div>
            <small style="color: #999;">Click to test navigation to quote detail page</small>
        </div>
        
        <div id="demo-results" class="test-result"></div>
    </div>

    <!-- Load required scripts -->
    <script src="js/config.js"></script>
    <script src="js/mock-data.js"></script>
    <script src="js/api-client.js"></script>
    <script src="js/url-handler.js"></script>

    <script>
        let testResults = {
            api: [],
            component: [],
            integration: [],
            overall: { passed: 0, failed: 0, total: 0 }
        };

        // Utility functions
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = message;
                element.className = `test-result ${type}`;
            }
        }

        function appendResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            if (element) {
                const timestamp = new Date().toLocaleTimeString();
                element.textContent += `[${timestamp}] ${message}\n`;
                element.className = `test-result ${type}`;
                element.scrollTop = element.scrollHeight;
            }
        }

        function updateOverallStatus() {
            const { passed, failed, total } = testResults.overall;
            const percentage = total > 0 ? Math.round((passed / total) * 100) : 0;
            const status = failed === 0 ? 'success' : (passed > failed ? 'info' : 'error');
            
            showResult('overall-status', 
                `Overall Test Results: ${passed}/${total} passed (${percentage}%)\n` +
                `✅ Passed: ${passed}\n` +
                `❌ Failed: ${failed}\n` +
                `Status: ${failed === 0 ? 'All tests passed!' : 'Some tests failed'}`,
                status
            );
        }

        function recordTestResult(category, testName, passed, details = '') {
            testResults[category].push({ testName, passed, details });
            testResults.overall.total++;
            if (passed) {
                testResults.overall.passed++;
            } else {
                testResults.overall.failed++;
            }
            updateOverallStatus();
        }

        // API Tests
        async function testGetQuoteAPI() {
            appendResult('api-test-results', '🧪 Testing getQuote API method...', 'loading');
            
            try {
                // Test valid quote ID
                const quote = await window.ApiClient.getQuote(1);
                if (quote && quote.id && quote.content) {
                    appendResult('api-test-results', 
                        `✅ getQuote(1) SUCCESS: Retrieved quote with ID ${quote.id}`, 'success');
                    recordTestResult('api', 'getQuote valid ID', true, `Quote: "${quote.content.substring(0, 50)}..."`);
                } else {
                    appendResult('api-test-results', '❌ getQuote(1) FAILED: Invalid response structure', 'error');
                    recordTestResult('api', 'getQuote valid ID', false, 'Invalid response structure');
                }

                // Test invalid quote ID
                const invalidQuote = await window.ApiClient.getQuote(-1);
                if (invalidQuote === null) {
                    appendResult('api-test-results', '✅ getQuote(-1) SUCCESS: Correctly returned null for invalid ID', 'success');
                    recordTestResult('api', 'getQuote invalid ID', true);
                } else {
                    appendResult('api-test-results', '❌ getQuote(-1) FAILED: Should return null for invalid ID', 'error');
                    recordTestResult('api', 'getQuote invalid ID', false);
                }

            } catch (error) {
                appendResult('api-test-results', `❌ API Test ERROR: ${error.message}`, 'error');
                recordTestResult('api', 'getQuote API', false, error.message);
            }
        }

        async function testAPIErrorHandling() {
            appendResult('api-test-results', '🧪 Testing API error handling...', 'loading');
            
            try {
                // Test null ID
                const nullResult = await window.ApiClient.getQuote(null);
                const undefinedResult = await window.ApiClient.getQuote(undefined);
                const stringResult = await window.ApiClient.getQuote("invalid");
                
                if (nullResult === null && undefinedResult === null && stringResult === null) {
                    appendResult('api-test-results', '✅ Error handling SUCCESS: All invalid inputs returned null', 'success');
                    recordTestResult('api', 'Error handling', true);
                } else {
                    appendResult('api-test-results', '❌ Error handling FAILED: Invalid inputs should return null', 'error');
                    recordTestResult('api', 'Error handling', false);
                }
            } catch (error) {
                appendResult('api-test-results', `❌ Error handling test ERROR: ${error.message}`, 'error');
                recordTestResult('api', 'Error handling', false, error.message);
            }
        }

        async function testAPICaching() {
            appendResult('api-test-results', '🧪 Testing API caching...', 'loading');
            
            try {
                const startTime1 = Date.now();
                const quote1 = await window.ApiClient.getQuote(1, true);
                const time1 = Date.now() - startTime1;

                const startTime2 = Date.now();
                const quote2 = await window.ApiClient.getQuote(1, true);
                const time2 = Date.now() - startTime2;

                if (quote1 && quote2 && quote1.id === quote2.id) {
                    appendResult('api-test-results', 
                        `✅ Caching SUCCESS: First call: ${time1}ms, Second call: ${time2}ms`, 'success');
                    recordTestResult('api', 'API caching', true, `Speedup: ${time1 - time2}ms`);
                } else {
                    appendResult('api-test-results', '❌ Caching FAILED: Inconsistent results', 'error');
                    recordTestResult('api', 'API caching', false);
                }
            } catch (error) {
                appendResult('api-test-results', `❌ Caching test ERROR: ${error.message}`, 'error');
                recordTestResult('api', 'API caching', false, error.message);
            }
        }

        // Component Tests
        function testQuoteCardClick() {
            appendResult('component-test-results', '🧪 Testing quote card click functionality...', 'loading');
            
            try {
                // Check if UrlHandler is available
                if (typeof UrlHandler === 'undefined' || typeof UrlHandler.getQuoteUrl !== 'function') {
                    appendResult('component-test-results', '❌ UrlHandler.getQuoteUrl not available', 'error');
                    recordTestResult('component', 'Quote card click', false, 'UrlHandler not available');
                    return;
                }

                // Test URL generation
                const testQuote = { id: 1, content: "Test quote" };
                const url = UrlHandler.getQuoteUrl(testQuote);
                
                if (url && url.includes('/quotes/1/')) {
                    appendResult('component-test-results', `✅ URL generation SUCCESS: ${url}`, 'success');
                    recordTestResult('component', 'Quote card click', true, `Generated URL: ${url}`);
                } else {
                    appendResult('component-test-results', `❌ URL generation FAILED: ${url}`, 'error');
                    recordTestResult('component', 'Quote card click', false, `Invalid URL: ${url}`);
                }
            } catch (error) {
                appendResult('component-test-results', `❌ Quote card test ERROR: ${error.message}`, 'error');
                recordTestResult('component', 'Quote card click', false, error.message);
            }
        }

        function testHeroCardClick() {
            appendResult('component-test-results', '🧪 Testing hero card click functionality...', 'loading');
            
            try {
                // Simulate hero card element
                const heroCard = document.createElement('div');
                heroCard.id = 'hero-quote-card';
                heroCard.setAttribute('data-quote-id', '1');
                heroCard.style.cursor = 'pointer';
                
                if (heroCard.style.cursor === 'pointer') {
                    appendResult('component-test-results', '✅ Hero card cursor style SUCCESS: pointer cursor set', 'success');
                    recordTestResult('component', 'Hero card click', true);
                } else {
                    appendResult('component-test-results', '❌ Hero card cursor style FAILED: cursor not set to pointer', 'error');
                    recordTestResult('component', 'Hero card click', false);
                }
            } catch (error) {
                appendResult('component-test-results', `❌ Hero card test ERROR: ${error.message}`, 'error');
                recordTestResult('component', 'Hero card click', false, error.message);
            }
        }

        function testURLGeneration() {
            appendResult('component-test-results', '🧪 Testing URL generation...', 'loading');
            
            try {
                if (typeof UrlHandler === 'undefined') {
                    appendResult('component-test-results', '❌ UrlHandler not available', 'error');
                    recordTestResult('component', 'URL generation', false, 'UrlHandler not available');
                    return;
                }

                const testCases = [
                    { id: 1, content: "Test quote 1" },
                    { id: 123, content: "Another test quote" },
                    { id: 999, content: "Long test quote with many words to test URL generation" }
                ];

                let allPassed = true;
                for (const testQuote of testCases) {
                    const url = UrlHandler.getQuoteUrl(testQuote);
                    if (!url || !url.includes(`/quotes/${testQuote.id}/`)) {
                        allPassed = false;
                        break;
                    }
                }

                if (allPassed) {
                    appendResult('component-test-results', `✅ URL generation SUCCESS: All ${testCases.length} test cases passed`, 'success');
                    recordTestResult('component', 'URL generation', true);
                } else {
                    appendResult('component-test-results', '❌ URL generation FAILED: Some test cases failed', 'error');
                    recordTestResult('component', 'URL generation', false);
                }
            } catch (error) {
                appendResult('component-test-results', `❌ URL generation test ERROR: ${error.message}`, 'error');
                recordTestResult('component', 'URL generation', false, error.message);
            }
        }

        // Integration Tests
        async function testEndToEndFlow() {
            appendResult('integration-test-results', '🧪 Testing end-to-end flow...', 'loading');
            
            try {
                // Step 1: Get quote data
                const quote = await window.ApiClient.getQuote(1);
                if (!quote) {
                    throw new Error('Failed to get quote data');
                }

                // Step 2: Generate URL
                const url = UrlHandler.getQuoteUrl(quote);
                if (!url) {
                    throw new Error('Failed to generate URL');
                }

                // Step 3: Simulate navigation (without actually navigating)
                const expectedPath = `/quotes/${quote.id}/`;
                if (url.includes(expectedPath)) {
                    appendResult('integration-test-results', 
                        `✅ End-to-end flow SUCCESS:\n` +
                        `1. Retrieved quote: "${quote.content.substring(0, 50)}..."\n` +
                        `2. Generated URL: ${url}\n` +
                        `3. URL contains expected path: ${expectedPath}`, 'success');
                    recordTestResult('integration', 'End-to-end flow', true);
                } else {
                    throw new Error(`URL does not contain expected path: ${expectedPath}`);
                }
            } catch (error) {
                appendResult('integration-test-results', `❌ End-to-end flow ERROR: ${error.message}`, 'error');
                recordTestResult('integration', 'End-to-end flow', false, error.message);
            }
        }

        function testPageNavigation() {
            appendResult('integration-test-results', '🧪 Testing page navigation...', 'loading');
            
            try {
                // Test if we can construct proper navigation URLs
                const testQuotes = [
                    { id: 1, content: "Quote 1" },
                    { id: 2, content: "Quote 2" },
                    { id: 3, content: "Quote 3" }
                ];

                let allValid = true;
                const results = [];

                for (const quote of testQuotes) {
                    const url = UrlHandler.getQuoteUrl(quote);
                    const isValid = url && url.includes(`/quotes/${quote.id}/`);
                    results.push(`Quote ${quote.id}: ${isValid ? '✅' : '❌'} ${url}`);
                    if (!isValid) allValid = false;
                }

                if (allValid) {
                    appendResult('integration-test-results', 
                        `✅ Page navigation SUCCESS:\n${results.join('\n')}`, 'success');
                    recordTestResult('integration', 'Page navigation', true);
                } else {
                    appendResult('integration-test-results', 
                        `❌ Page navigation FAILED:\n${results.join('\n')}`, 'error');
                    recordTestResult('integration', 'Page navigation', false);
                }
            } catch (error) {
                appendResult('integration-test-results', `❌ Page navigation ERROR: ${error.message}`, 'error');
                recordTestResult('integration', 'Page navigation', false, error.message);
            }
        }

        async function testDataFlow() {
            appendResult('integration-test-results', '🧪 Testing data flow...', 'loading');
            
            try {
                // Test the complete data flow from API to UI
                const quote = await window.ApiClient.getQuote(1);
                
                if (!quote) {
                    throw new Error('No quote data received');
                }

                // Verify data structure
                const requiredFields = ['id', 'content', 'author'];
                const missingFields = requiredFields.filter(field => !quote[field]);
                
                if (missingFields.length > 0) {
                    throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
                }

                // Verify data types
                if (typeof quote.id !== 'number' && typeof quote.id !== 'string') {
                    throw new Error('Quote ID must be a number or string');
                }

                if (typeof quote.content !== 'string' || quote.content.length === 0) {
                    throw new Error('Quote content must be a non-empty string');
                }

                if (!quote.author || typeof quote.author.name !== 'string') {
                    throw new Error('Quote author must have a name');
                }

                appendResult('integration-test-results', 
                    `✅ Data flow SUCCESS:\n` +
                    `- Quote ID: ${quote.id} (${typeof quote.id})\n` +
                    `- Content length: ${quote.content.length} characters\n` +
                    `- Author: ${quote.author.name}\n` +
                    `- Categories: ${quote.categories?.length || 0}\n` +
                    `- Sources: ${quote.sources?.length || 0}`, 'success');
                recordTestResult('integration', 'Data flow', true);
            } catch (error) {
                appendResult('integration-test-results', `❌ Data flow ERROR: ${error.message}`, 'error');
                recordTestResult('integration', 'Data flow', false, error.message);
            }
        }

        // Demo functions
        function testQuoteCardNavigation(quoteId) {
            appendResult('demo-results', `🔗 Testing navigation to quote ${quoteId}...`, 'loading');
            
            try {
                const url = UrlHandler.getQuoteUrl({ id: quoteId, content: `Test quote ${quoteId}` });
                appendResult('demo-results', 
                    `✅ Navigation test SUCCESS:\n` +
                    `Generated URL: ${url}\n` +
                    `In a real scenario, this would navigate to the quote detail page.`, 'success');
                
                // In a real implementation, you would do:
                // window.location.href = url;
                
                // For testing, we'll just show what would happen
                setTimeout(() => {
                    appendResult('demo-results', 
                        `ℹ️ To actually test navigation, uncomment the line:\n` +
                        `// window.location.href = "${url}";`, 'info');
                }, 1000);
                
            } catch (error) {
                appendResult('demo-results', `❌ Navigation test ERROR: ${error.message}`, 'error');
            }
        }

        function openQuoteDetail(quoteId) {
            const url = UrlHandler.getQuoteUrl({ id: quoteId, content: `Test quote ${quoteId}` });
            window.open(url, '_blank');
        }

        // Main test runner
        async function runAllTests() {
            // Clear previous results
            testResults = {
                api: [],
                component: [],
                integration: [],
                overall: { passed: 0, failed: 0, total: 0 }
            };

            showResult('overall-status', 'Running all tests...', 'loading');
            
            // Clear all result areas
            ['api-test-results', 'component-test-results', 'integration-test-results', 'demo-results'].forEach(id => {
                showResult(id, '', 'info');
            });

            try {
                // Run API tests
                await testGetQuoteAPI();
                await testAPIErrorHandling();
                await testAPICaching();

                // Run component tests
                testQuoteCardClick();
                testHeroCardClick();
                testURLGeneration();

                // Run integration tests
                await testEndToEndFlow();
                testPageNavigation();
                await testDataFlow();

                updateOverallStatus();
                
            } catch (error) {
                showResult('overall-status', `Test suite ERROR: ${error.message}`, 'error');
            }
        }

        function clearResults() {
            ['api-test-results', 'component-test-results', 'integration-test-results', 'demo-results', 'overall-status'].forEach(id => {
                showResult(id, '', 'info');
            });
            
            testResults = {
                api: [],
                component: [],
                integration: [],
                overall: { passed: 0, failed: 0, total: 0 }
            };
            
            showResult('overall-status', 'Results cleared. Ready to run tests...', 'info');
        }

        // Initialize page
        window.addEventListener('load', function() {
            console.log('Quote Detail Test Suite loaded');
            console.log('Available objects:', {
                ApiClient: !!window.ApiClient,
                UrlHandler: !!window.UrlHandler,
                getQuote: typeof window.ApiClient?.getQuote
            });
            
            showResult('overall-status', 
                'Test suite ready!\n' +
                `ApiClient available: ${!!window.ApiClient}\n` +
                `UrlHandler available: ${!!window.UrlHandler}\n` +
                `getQuote method: ${typeof window.ApiClient?.getQuote}`, 'info');
        });
    </script>
</body>
</html>
