<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Famous Authors | Quote Authors Collection - Quotese.com</title>
    <meta name="description" content="Browse quotes by famous authors. Discover wisdom from great minds throughout history.">
    <meta name="keywords" content="famous authors, quote authors, writers, philosophers, inspirational figures">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/css/variables.css">
    <link rel="stylesheet" href="/css/styles.css">
    <link rel="stylesheet" href="/css/responsive.css">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <div id="navigation-container"></div>
    
    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto">
            <!-- Page Header -->
            <div class="text-center mb-12">
                <h1 class="text-4xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-users mr-3 text-blue-500"></i>
                    Famous Authors
                </h1>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Discover inspiring quotes from renowned authors, writers, and thinkers throughout history.
                </p>
            </div>
            
            <!-- Search Bar -->
            <div class="mb-8">
                <div class="max-w-md mx-auto">
                    <div class="relative">
                        <input type="text" id="author-search" placeholder="Search authors..." 
                               class="w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                </div>
            </div>
            
            <!-- Authors Grid -->
            <div id="authors-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Authors will be loaded here -->
            </div>
            
            <!-- Pagination -->
            <div id="pagination-container" class="mt-12"></div>
        </div>
    </main>
    
    <!-- Footer -->
    <div id="footer-container"></div>
    
    <!-- Core Scripts -->
    <script src="/js/theme.js"></script>
    <script src="/js/url-handler.js"></script>
    <script src="/js/seo-manager.js"></script>
    <script src="/js/page-router.js"></script>
    <script src="/js/mobile-menu.js"></script>
    <script src="/js/components/pagination.js"></script>
    <script src="/js/components/breadcrumb.js"></script>
    <script src="/js/social-meta.js"></script>

    <!-- Page Scripts -->
    <script src="/js/pages/authors.js"></script>

    <!-- Component Loader -->
    <script src="/js/component-loader.js"></script>
</body>
</html>
