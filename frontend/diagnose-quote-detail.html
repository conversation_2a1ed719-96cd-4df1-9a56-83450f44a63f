<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>名言详情页跳转问题诊断</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .diagnostic-container {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .diagnostic-title {
            color: #333;
            border-bottom: 2px solid #dc3545;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .test-button {
            background: #dc3545;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #c82333;
        }
        .test-button.success {
            background: #28a745;
        }
        .test-button.warning {
            background: #ffc107;
            color: #212529;
        }
        .diagnostic-result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .quote-card-test {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease, border-color 0.2s ease;
            background: white;
        }
        .quote-card-test.cursor-pointer:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
            border-color: #ffd300;
        }
        .quote-content {
            font-size: 18px;
            font-style: italic;
            margin-bottom: 15px;
            line-height: 1.6;
        }
        .quote-author {
            font-weight: bold;
            color: #666;
            margin-bottom: 10px;
        }
        .step-indicator {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #007bff;
            color: white;
            text-align: center;
            line-height: 20px;
            font-size: 12px;
            margin-right: 10px;
        }
        .step-indicator.success {
            background: #28a745;
        }
        .step-indicator.error {
            background: #dc3545;
        }
        .step-indicator.warning {
            background: #ffc107;
            color: #212529;
        }
    </style>
</head>
<body>
    <div class="diagnostic-container">
        <h1 class="diagnostic-title">🔍 名言详情页跳转问题诊断</h1>
        <p>本页面专门诊断名言卡片点击跳转到详情页的功能问题。</p>
        
        <div style="display: flex; gap: 10px; margin-bottom: 20px;">
            <button class="test-button" onclick="runFullDiagnostic()">🚨 运行完整诊断</button>
            <button class="test-button success" onclick="testQuoteDetailPage()">📄 测试详情页</button>
            <button class="test-button warning" onclick="clearResults()">🧹 清除结果</button>
        </div>

        <div id="overall-diagnostic" class="diagnostic-result info">
            准备开始诊断...
        </div>
    </div>

    <!-- 步骤1: 基础环境检查 -->
    <div class="diagnostic-container">
        <h2 class="diagnostic-title"><span class="step-indicator">1</span>基础环境检查</h2>
        
        <button class="test-button" onclick="checkEnvironment()">检查环境</button>
        <div id="environment-result" class="diagnostic-result"></div>
    </div>

    <!-- 步骤2: API功能检查 -->
    <div class="diagnostic-container">
        <h2 class="diagnostic-title"><span class="step-indicator">2</span>API功能检查</h2>
        
        <button class="test-button" onclick="checkAPIFunctionality()">检查API</button>
        <div id="api-result" class="diagnostic-result"></div>
    </div>

    <!-- 步骤3: 详情页文件检查 -->
    <div class="diagnostic-container">
        <h2 class="diagnostic-title"><span class="step-indicator">3</span>详情页文件检查</h2>
        
        <button class="test-button" onclick="checkQuoteDetailFiles()">检查文件</button>
        <div id="files-result" class="diagnostic-result"></div>
    </div>

    <!-- 步骤4: 路由配置检查 -->
    <div class="diagnostic-container">
        <h2 class="diagnostic-title"><span class="step-indicator">4</span>路由配置检查</h2>
        
        <button class="test-button" onclick="checkRouterConfiguration()">检查路由</button>
        <div id="router-result" class="diagnostic-result"></div>
    </div>

    <!-- 步骤5: 点击事件测试 -->
    <div class="diagnostic-container">
        <h2 class="diagnostic-title"><span class="step-indicator">5</span>点击事件测试</h2>
        
        <p>点击下面的测试卡片来验证点击事件：</p>
        
        <div class="quote-card-test cursor-pointer" onclick="testCardClick(1, this)">
            <div class="quote-content">"Imagination is more important than knowledge."</div>
            <div class="quote-author">— Albert Einstein</div>
            <small style="color: #999;">测试卡片 - 点击测试跳转</small>
        </div>
        
        <button class="test-button" onclick="simulateCardClick()">模拟卡片点击</button>
        <div id="click-result" class="diagnostic-result"></div>
    </div>

    <!-- 步骤6: 详情页直接访问测试 -->
    <div class="diagnostic-container">
        <h2 class="diagnostic-title"><span class="step-indicator">6</span>详情页直接访问测试</h2>
        
        <button class="test-button" onclick="testDirectAccess()">测试直接访问</button>
        <button class="test-button success" onclick="openQuoteDetailPage()">打开详情页</button>
        <div id="direct-access-result" class="diagnostic-result"></div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="js/config.js"></script>
    <script src="js/mock-data.js"></script>
    <script src="js/api-client.js"></script>
    <script src="js/url-handler.js"></script>
    <script src="js/page-router.js"></script>

    <script>
        let diagnosticResults = {
            environment: null,
            api: null,
            files: null,
            router: null,
            click: null,
            directAccess: null
        };

        // 工具函数
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = message;
                element.className = `diagnostic-result ${type}`;
            }
        }

        function updateStepIndicator(stepNumber, status) {
            const indicators = document.querySelectorAll('.step-indicator');
            if (indicators[stepNumber - 1]) {
                indicators[stepNumber - 1].className = `step-indicator ${status}`;
            }
        }

        // 步骤1: 基础环境检查
        function checkEnvironment() {
            showResult('environment-result', '正在检查基础环境...', 'info');
            
            try {
                const checks = {
                    'ApiClient': typeof window.ApiClient !== 'undefined',
                    'UrlHandler': typeof UrlHandler !== 'undefined',
                    'PageRouter': typeof PageRouter !== 'undefined',
                    'AppConfig': typeof window.AppConfig !== 'undefined',
                    'getQuote方法': typeof window.ApiClient?.getQuote === 'function',
                    'getQuoteUrl方法': typeof UrlHandler?.getQuoteUrl === 'function'
                };

                let allPassed = true;
                let resultText = '环境检查结果:\n\n';

                for (const [name, passed] of Object.entries(checks)) {
                    resultText += `${passed ? '✅' : '❌'} ${name}: ${passed ? '可用' : '不可用'}\n`;
                    if (!passed) allPassed = false;
                }

                resultText += `\n当前配置:\n`;
                resultText += `- API端点: ${window.AppConfig?.graphqlEndpoint || '未配置'}\n`;
                resultText += `- 调试模式: ${window.AppConfig?.debug || false}\n`;
                resultText += `- 模拟数据: ${window.AppConfig?.useMockData || false}`;

                diagnosticResults.environment = allPassed;
                updateStepIndicator(1, allPassed ? 'success' : 'error');
                showResult('environment-result', resultText, allPassed ? 'success' : 'error');

            } catch (error) {
                diagnosticResults.environment = false;
                updateStepIndicator(1, 'error');
                showResult('environment-result', `环境检查失败: ${error.message}`, 'error');
            }
        }

        // 步骤2: API功能检查
        async function checkAPIFunctionality() {
            showResult('api-result', '正在检查API功能...', 'info');
            
            try {
                // 测试getQuote方法
                const quote = await window.ApiClient.getQuote(1);
                
                if (quote && quote.id && quote.content && quote.author) {
                    const resultText = 
                        `✅ API功能检查通过!\n\n` +
                        `获取的名言数据:\n` +
                        `- ID: ${quote.id}\n` +
                        `- 内容: "${quote.content.substring(0, 50)}..."\n` +
                        `- 作者: ${quote.author.name}\n` +
                        `- 分类数量: ${quote.categories?.length || 0}\n` +
                        `- 来源数量: ${quote.sources?.length || 0}`;
                    
                    diagnosticResults.api = true;
                    updateStepIndicator(2, 'success');
                    showResult('api-result', resultText, 'success');
                } else {
                    diagnosticResults.api = false;
                    updateStepIndicator(2, 'error');
                    showResult('api-result', '❌ API返回数据格式不正确', 'error');
                }

            } catch (error) {
                diagnosticResults.api = false;
                updateStepIndicator(2, 'error');
                showResult('api-result', `❌ API功能检查失败: ${error.message}`, 'error');
            }
        }

        // 步骤3: 详情页文件检查
        async function checkQuoteDetailFiles() {
            showResult('files-result', '正在检查详情页文件...', 'info');
            
            try {
                const filesToCheck = [
                    '/quote.html',
                    '/js/pages/quote.js',
                    '/js/components/quote-card.js'
                ];

                let resultText = '文件存在性检查:\n\n';
                let allFilesExist = true;

                for (const file of filesToCheck) {
                    try {
                        const response = await fetch(file, { method: 'HEAD' });
                        const exists = response.ok;
                        resultText += `${exists ? '✅' : '❌'} ${file}: ${exists ? '存在' : '不存在'}\n`;
                        if (!exists) allFilesExist = false;
                    } catch (error) {
                        resultText += `❌ ${file}: 检查失败 (${error.message})\n`;
                        allFilesExist = false;
                    }
                }

                diagnosticResults.files = allFilesExist;
                updateStepIndicator(3, allFilesExist ? 'success' : 'error');
                showResult('files-result', resultText, allFilesExist ? 'success' : 'error');

            } catch (error) {
                diagnosticResults.files = false;
                updateStepIndicator(3, 'error');
                showResult('files-result', `文件检查失败: ${error.message}`, 'error');
            }
        }

        // 步骤4: 路由配置检查
        function checkRouterConfiguration() {
            showResult('router-result', '正在检查路由配置...', 'info');
            
            try {
                let resultText = '路由配置检查:\n\n';
                let routerOK = true;

                // 检查PageRouter是否存在
                if (typeof PageRouter === 'undefined') {
                    resultText += '❌ PageRouter未定义\n';
                    routerOK = false;
                } else {
                    resultText += '✅ PageRouter已定义\n';
                    
                    // 检查路由映射
                    if (PageRouter.routes && PageRouter.routes['quote-detail']) {
                        resultText += '✅ quote-detail路由已配置\n';
                        resultText += `   映射到: ${PageRouter.routes['quote-detail']}\n`;
                    } else {
                        resultText += '❌ quote-detail路由未配置\n';
                        routerOK = false;
                    }
                }

                // 检查URL生成
                try {
                    const testQuote = { id: 1, content: "Test" };
                    const url = UrlHandler.getQuoteUrl(testQuote);
                    resultText += `✅ URL生成测试: ${url}\n`;
                } catch (error) {
                    resultText += `❌ URL生成失败: ${error.message}\n`;
                    routerOK = false;
                }

                diagnosticResults.router = routerOK;
                updateStepIndicator(4, routerOK ? 'success' : 'error');
                showResult('router-result', resultText, routerOK ? 'success' : 'error');

            } catch (error) {
                diagnosticResults.router = false;
                updateStepIndicator(4, 'error');
                showResult('router-result', `路由检查失败: ${error.message}`, 'error');
            }
        }

        // 步骤5: 点击事件测试
        function testCardClick(quoteId, element) {
            showResult('click-result', `正在测试卡片点击 (ID: ${quoteId})...`, 'info');
            
            try {
                const testQuote = { id: quoteId, content: element.querySelector('.quote-content').textContent };
                const url = UrlHandler.getQuoteUrl(testQuote);
                
                const resultText = 
                    `✅ 卡片点击测试成功!\n\n` +
                    `点击信息:\n` +
                    `- 名言ID: ${quoteId}\n` +
                    `- 生成URL: ${url}\n` +
                    `- 点击元素: ${element.tagName}\n` +
                    `- 样式类: ${element.className}\n\n` +
                    `注意: 这是测试模式，实际不会跳转`;

                diagnosticResults.click = true;
                updateStepIndicator(5, 'success');
                showResult('click-result', resultText, 'success');

                // 高亮点击的卡片
                element.style.borderColor = '#28a745';
                element.style.borderWidth = '2px';
                setTimeout(() => {
                    element.style.borderColor = '';
                    element.style.borderWidth = '';
                }, 2000);

            } catch (error) {
                diagnosticResults.click = false;
                updateStepIndicator(5, 'error');
                showResult('click-result', `卡片点击测试失败: ${error.message}`, 'error');
            }
        }

        function simulateCardClick() {
            showResult('click-result', '正在模拟名言卡片点击...', 'info');
            
            try {
                // 模拟真实的名言卡片点击逻辑
                const mockQuote = {
                    id: 1,
                    content: "Imagination is more important than knowledge.",
                    author: { name: "Albert Einstein" }
                };

                // 测试URL生成
                const url = UrlHandler.getQuoteUrl(mockQuote);
                
                // 模拟点击事件处理
                const mockEvent = {
                    target: { closest: () => null }, // 模拟不是按钮或链接
                    preventDefault: () => {},
                    stopPropagation: () => {}
                };

                const resultText = 
                    `✅ 模拟点击成功!\n\n` +
                    `模拟数据:\n` +
                    `- 名言: "${mockQuote.content}"\n` +
                    `- 作者: ${mockQuote.author.name}\n` +
                    `- 生成URL: ${url}\n\n` +
                    `点击逻辑验证:\n` +
                    `- URL生成: ✅\n` +
                    `- 事件处理: ✅\n` +
                    `- 跳转准备: ✅`;

                diagnosticResults.click = true;
                updateStepIndicator(5, 'success');
                showResult('click-result', resultText, 'success');

            } catch (error) {
                diagnosticResults.click = false;
                updateStepIndicator(5, 'error');
                showResult('click-result', `模拟点击失败: ${error.message}`, 'error');
            }
        }

        // 步骤6: 详情页直接访问测试
        async function testDirectAccess() {
            showResult('direct-access-result', '正在测试详情页直接访问...', 'info');
            
            try {
                const testUrl = '/quotes/1/';
                const fullUrl = `${window.location.origin}${testUrl}`;
                
                // 测试URL可访问性
                const response = await fetch(fullUrl, { method: 'HEAD' });
                
                let resultText = `详情页直接访问测试:\n\n`;
                resultText += `测试URL: ${fullUrl}\n`;
                resultText += `HTTP状态: ${response.status} ${response.statusText}\n`;
                
                if (response.ok) {
                    resultText += `✅ 详情页可直接访问\n`;
                    diagnosticResults.directAccess = true;
                    updateStepIndicator(6, 'success');
                    showResult('direct-access-result', resultText, 'success');
                } else {
                    resultText += `❌ 详情页无法访问\n`;
                    resultText += `可能原因: 文件不存在或路由未配置`;
                    diagnosticResults.directAccess = false;
                    updateStepIndicator(6, 'error');
                    showResult('direct-access-result', resultText, 'error');
                }

            } catch (error) {
                diagnosticResults.directAccess = false;
                updateStepIndicator(6, 'error');
                showResult('direct-access-result', `直接访问测试失败: ${error.message}`, 'error');
            }
        }

        function openQuoteDetailPage() {
            const url = '/quotes/1/';
            window.open(url, '_blank');
            showResult('direct-access-result', `已在新窗口打开详情页: ${url}`, 'info');
        }

        function testQuoteDetailPage() {
            // 测试详情页是否存在
            window.open('/quote.html?id=1', '_blank');
        }

        // 运行完整诊断
        async function runFullDiagnostic() {
            showResult('overall-diagnostic', '正在运行完整诊断...', 'info');
            
            // 重置所有指示器
            for (let i = 1; i <= 6; i++) {
                updateStepIndicator(i, '');
            }
            
            try {
                // 按顺序运行所有检查
                checkEnvironment();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                await checkAPIFunctionality();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                await checkQuoteDetailFiles();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                checkRouterConfiguration();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                simulateCardClick();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                await testDirectAccess();
                
                // 生成总结报告
                const results = Object.values(diagnosticResults);
                const passed = results.filter(r => r === true).length;
                const total = results.length;
                
                let summaryText = `🔍 完整诊断完成!\n\n`;
                summaryText += `总体结果: ${passed}/${total} 项检查通过\n\n`;
                summaryText += `详细结果:\n`;
                summaryText += `1. 基础环境: ${diagnosticResults.environment ? '✅' : '❌'}\n`;
                summaryText += `2. API功能: ${diagnosticResults.api ? '✅' : '❌'}\n`;
                summaryText += `3. 文件存在: ${diagnosticResults.files ? '✅' : '❌'}\n`;
                summaryText += `4. 路由配置: ${diagnosticResults.router ? '✅' : '❌'}\n`;
                summaryText += `5. 点击事件: ${diagnosticResults.click ? '✅' : '❌'}\n`;
                summaryText += `6. 直接访问: ${diagnosticResults.directAccess ? '✅' : '❌'}\n\n`;
                
                if (passed === total) {
                    summaryText += `🎉 所有检查都通过了！名言详情页功能应该正常工作。`;
                } else {
                    summaryText += `⚠️ 发现 ${total - passed} 个问题，请查看具体检查结果。`;
                }

                const overallStatus = passed === total ? 'success' : (passed > total / 2 ? 'warning' : 'error');
                showResult('overall-diagnostic', summaryText, overallStatus);

            } catch (error) {
                showResult('overall-diagnostic', `诊断过程出错: ${error.message}`, 'error');
            }
        }

        function clearResults() {
            // 清除所有结果
            const resultElements = document.querySelectorAll('.diagnostic-result');
            resultElements.forEach(element => {
                element.textContent = '';
                element.className = 'diagnostic-result info';
            });
            
            // 重置指示器
            for (let i = 1; i <= 6; i++) {
                updateStepIndicator(i, '');
            }
            
            // 重置结果
            diagnosticResults = {
                environment: null,
                api: null,
                files: null,
                router: null,
                click: null,
                directAccess: null
            };
            
            showResult('overall-diagnostic', '诊断结果已清除，准备重新开始...', 'info');
        }

        // 页面加载时初始化
        window.addEventListener('load', function() {
            console.log('名言详情页跳转问题诊断页面已加载');
            showResult('overall-diagnostic', 
                '🔍 诊断工具已准备就绪!\n\n' +
                '点击"运行完整诊断"开始全面检查，\n' +
                '或者点击各个步骤单独检查特定问题。\n\n' +
                '这个工具将帮助您找出名言卡片点击跳转问题的根本原因。', 'info');
        });
    </script>
</body>
</html>
