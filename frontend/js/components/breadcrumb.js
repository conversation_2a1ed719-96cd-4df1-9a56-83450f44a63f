/**
 * 面包屑导航组件 - 重构版本
 * 支持新的语义化URL格式和UrlHandler 2.0
 *
 * @version 2.0.0
 * @date 2025-06-16
 * <AUTHOR>
 */
class BreadcrumbComponent {
  /**
   * 渲染面包屑导航
   * @param {string} containerId - 容器ID
   * @param {Array} items - 面包屑项目数组，每项包含name、url和active属性
   */
  static render(containerId, items) {
    const container = document.getElementById(containerId);
    if (!container) {
      console.warn(`Breadcrumb container with ID '${containerId}' not found`);
      return;
    }

    // 清空容器
    container.innerHTML = '';

    // 创建面包屑列表
    const breadcrumbList = document.createElement('ol');
    breadcrumbList.className = 'breadcrumb-list flex items-center space-x-2 text-sm';
    breadcrumbList.setAttribute('aria-label', 'Breadcrumb navigation');

    // 渲染每个面包屑项目
    items.forEach((item, index) => {
      const listItem = document.createElement('li');
      listItem.className = 'breadcrumb-item flex items-center';

      // 添加分隔符（除了第一项）
      if (index > 0) {
        const separator = document.createElement('span');
        separator.className = 'breadcrumb-separator mx-2 text-gray-400';
        separator.textContent = '/';
        separator.setAttribute('aria-hidden', 'true');
        listItem.appendChild(separator);
      }

      // 创建链接或文本
      if (item.active) {
        // 当前页面，显示为文本
        const currentItem = document.createElement('span');
        currentItem.className = 'breadcrumb-current text-gray-700 dark:text-gray-300 font-medium';
        currentItem.textContent = item.name;
        currentItem.setAttribute('aria-current', 'page');
        listItem.appendChild(currentItem);
      } else {
        // 可点击的链接
        const link = document.createElement('a');
        link.href = item.url;
        link.className = 'breadcrumb-link text-blue-600 hover:text-yellow-600 dark:text-blue-400 dark:hover:text-yellow-400 transition-colors duration-200';
        link.textContent = item.name;
        link.setAttribute('title', `Go to ${item.name}`);

        // 添加点击事件处理（支持SPA导航）
        link.addEventListener('click', (e) => {
          if (UrlHandler.isInternalUrl(item.url)) {
            e.preventDefault();
            UrlHandler.navigateTo(item.url);
          }
        });

        listItem.appendChild(link);
      }

      breadcrumbList.appendChild(listItem);
    });

    container.appendChild(breadcrumbList);

    // 添加结构化数据
    BreadcrumbComponent.addStructuredData(items);
  }

  /**
   * 添加结构化数据（Schema.org BreadcrumbList）
   * @param {Array} items - 面包屑项目数组
   */
  static addStructuredData(items) {
    // 移除旧的结构化数据
    const oldScript = document.getElementById('breadcrumb-structured-data');
    if (oldScript) {
      oldScript.remove();
    }

    // 过滤掉没有URL的项目，并确保URL格式正确
    const validItems = items.filter(item => item.url && item.url !== '#');

    if (validItems.length === 0) {
      return; // 没有有效的面包屑项目
    }

    // 创建结构化数据
    const structuredData = {
      "@context": "https://schema.org",
      "@type": "BreadcrumbList",
      "itemListElement": validItems.map((item, index) => {
        // 确保URL是完整的
        let itemUrl = item.url;
        if (!itemUrl.startsWith('http')) {
          itemUrl = `${UrlHandler.CONFIG.BASE_URL}${itemUrl.startsWith('/') ? itemUrl : '/' + itemUrl}`;
        }

        return {
          "@type": "ListItem",
          "position": index + 1,
          "name": item.name,
          "item": itemUrl
        };
      })
    };

    // 添加到页面
    const script = document.createElement('script');
    script.id = 'breadcrumb-structured-data';
    script.type = 'application/ld+json';
    script.textContent = JSON.stringify(structuredData, null, 2);
    document.head.appendChild(script);
  }

  /**
   * 使用新的UrlHandler生成面包屑数据
   * @returns {Array} 面包屑项目数组，包含name、url和active属性
   */
  static generateBreadcrumbItems() {
    // 直接使用UrlHandler的getBreadcrumbData方法
    return UrlHandler.getBreadcrumbData();
  }

  /**
   * 自动初始化面包屑导航（基于当前URL）
   * @param {string} containerId - 容器ID
   */
  static autoInit(containerId) {
    const items = BreadcrumbComponent.generateBreadcrumbItems();
    BreadcrumbComponent.render(containerId, items);
  }

  /**
   * 手动设置面包屑导航
   * @param {string} containerId - 容器ID
   * @param {Array} customItems - 自定义面包屑项目数组
   */
  static setCustomBreadcrumb(containerId, customItems) {
    // 确保所有项目都有必要的属性
    const normalizedItems = customItems.map((item, index) => ({
      name: item.name || 'Unknown',
      url: item.url || '#',
      active: item.active !== undefined ? item.active : (index === customItems.length - 1)
    }));

    BreadcrumbComponent.render(containerId, normalizedItems);
  }

  /**
   * 更新面包屑导航（用于SPA路由变化）
   * @param {string} containerId - 容器ID
   */
  static update(containerId) {
    BreadcrumbComponent.autoInit(containerId);
  }

  /**
   * 清除面包屑导航
   * @param {string} containerId - 容器ID
   */
  static clear(containerId) {
    const container = document.getElementById(containerId);
    if (container) {
      container.innerHTML = '';
    }

    // 移除结构化数据
    const structuredDataScript = document.getElementById('breadcrumb-structured-data');
    if (structuredDataScript) {
      structuredDataScript.remove();
    }
  }

  /**
   * 向后兼容的初始化方法
   * @deprecated 使用 autoInit() 替代
   * @param {string} containerId - 容器ID
   */
  static init(containerId) {
    console.warn('BreadcrumbComponent.init() is deprecated. Use autoInit() instead.');
    BreadcrumbComponent.autoInit(containerId);
  }
}

// 监听URL变化事件，自动更新面包屑
window.addEventListener('urlChanged', function(event) {
  // 查找页面中的面包屑容器并更新
  const breadcrumbContainers = document.querySelectorAll('[data-breadcrumb-container]');
  breadcrumbContainers.forEach(container => {
    BreadcrumbComponent.update(container.id);
  });
});

// 导出组件
window.BreadcrumbComponent = BreadcrumbComponent;
