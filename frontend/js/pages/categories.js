/**
 * Categories List Page Controller
 * Independent implementation for /categories/ page
 * Uses same APIs as Popular Categories module but with different logic
 */

// Page state - using independent namespace to avoid conflicts
const categoriesListPageState = {
    // Data state
    allCategories: [],
    displayedCategories: [],
    filteredCategories: [],
    
    // Pagination state
    currentPage: 1,
    pageSize: 48,  // Grid view: 6x8 or 4x12
    totalPages: 0,
    totalCount: 0,
    
    // UI state
    isLoading: false,
    searchQuery: '',
    sortOrder: 'popularity',
    viewMode: 'grid',
    
    // Performance state
    loadedCount: 0,
    maxCategories: 500,
    hasMore: true
};

/**
 * Page initialization function
 * Called by PageRouter: initCategoriesListPage()
 */
async function initCategoriesListPage(params) {
    try {
        console.log('🚀 Initializing Categories List Page...');
        console.log('📋 Params received:', params);
        console.log('🔍 Available global objects:', {
            ApiClient: !!window.ApiClient,
            ComponentLoader: !!window.ComponentLoader,
            PageRouter: !!window.PageRouter,
            UrlHandler: !!window.UrlHandler
        });

        // Show loading state
        showLoadingState();
        console.log('⏳ Loading state shown');

        // Load page components
        try {
            await loadPageComponents();
            console.log('✅ Page components loaded');
        } catch (componentError) {
            console.warn('⚠️ Component loading failed, continuing anyway:', componentError);
        }

        // Load categories data
        try {
            await loadCategoriesData();
            console.log('✅ Categories data loaded');
        } catch (dataError) {
            console.error('❌ Categories data loading failed:', dataError);
            throw dataError; // This is critical, so we should fail
        }

        // Initialize UI controls
        try {
            initializeControls();
            console.log('✅ UI controls initialized');
        } catch (controlsError) {
            console.warn('⚠️ UI controls initialization failed:', controlsError);
        }

        // Note: Sidebar content removed from Categories list page for cleaner design
        console.log('✅ Sidebar content skipped (not needed for Categories list page)');

        // Hide loading state
        hideLoadingState();
        console.log('✅ Loading state hidden');

        console.log('🎉 Categories List Page initialized successfully');
    } catch (error) {
        console.error('💥 Error initializing Categories List Page:', error);
        console.error('Stack trace:', error.stack);
        showErrorState(error);
    }
}

/**
 * Load page components (breadcrumb, navigation, footer)
 */
async function loadPageComponents() {
    try {
        console.log('Loading page components...');

        // Wait for ComponentLoader to be available
        let attempts = 0;
        while (!window.ComponentLoader && attempts < 50) {
            console.log(`Waiting for ComponentLoader... attempt ${attempts + 1}`);
            await new Promise(resolve => setTimeout(resolve, 100));
            attempts++;
        }

        if (!window.ComponentLoader) {
            console.warn('ComponentLoader not available after waiting, using fallback');
            loadComponentsFallback();
            return;
        }

        console.log('ComponentLoader available, loading components...');

        // Load breadcrumb
        const breadcrumbResult = await window.ComponentLoader.loadComponent('breadcrumb-container', 'breadcrumb');
        console.log('Breadcrumb loaded:', breadcrumbResult);

        // Load navigation
        const navResult = await window.ComponentLoader.loadComponent('navigation-container', 'navigation');
        console.log('Navigation loaded:', navResult);

        // Load footer
        const footerResult = await window.ComponentLoader.loadComponent('footer-container', 'footer');
        console.log('Footer loaded:', footerResult);

        console.log('All components loaded successfully');
    } catch (error) {
        console.warn('Some components failed to load:', error);
        loadComponentsFallback();
    }
}

/**
 * Fallback component loading when ComponentLoader is not available
 */
function loadComponentsFallback() {
    console.log('Using fallback component loading...');

    // Simple navigation fallback
    const navContainer = document.getElementById('navigation-container');
    if (navContainer) {
        navContainer.innerHTML = `
            <nav class="sticky top-0 z-50 bg-white dark:bg-gray-900 shadow-md">
                <div class="container mx-auto px-4 py-3">
                    <div class="flex justify-between items-center">
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-quote-left text-yellow-500 text-2xl"></i>
                            <h1 class="text-xl font-bold">Quotese.com</h1>
                        </div>
                        <div class="hidden md:flex space-x-6">
                            <a href="/" class="nav-link font-medium text-gray-600 hover:text-yellow-500">Home</a>
                            <a href="/authors/" class="nav-link font-medium text-gray-600 hover:text-yellow-500">Authors</a>
                            <a href="/categories/" class="nav-link font-medium text-yellow-500 hover:text-yellow-600">Categories</a>
                            <a href="/sources/" class="nav-link font-medium text-gray-600 hover:text-yellow-500">Sources</a>
                        </div>
                    </div>
                </div>
            </nav>
        `;
    }

    // Simple breadcrumb fallback
    const breadcrumbContainer = document.getElementById('breadcrumb-container');
    if (breadcrumbContainer) {
        breadcrumbContainer.innerHTML = `
            <nav class="flex" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="/" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                            <i class="fas fa-home mr-2"></i>Home
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <i class="fas fa-chevron-right text-gray-400 mx-1"></i>
                            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">Categories</span>
                        </div>
                    </li>
                </ol>
            </nav>
        `;
    }

    // Simple footer fallback
    const footerContainer = document.getElementById('footer-container');
    if (footerContainer) {
        footerContainer.innerHTML = `
            <footer class="bg-gray-100 dark:bg-gray-900 border-t border-gray-200 dark:border-gray-800 mt-12">
                <div class="container mx-auto px-4 py-8 text-center">
                    <p class="text-sm text-gray-600 dark:text-gray-400">&copy; 2023 Quotese.com. All rights reserved.</p>
                </div>
            </footer>
        `;
    }
}

/**
 * Load categories data
 * Uses same API as Popular Categories module but with different logic
 */
async function loadCategoriesData() {
    try {
        console.log('Loading categories data...');

        // Check if ApiClient is available
        if (!window.ApiClient) {
            throw new Error('ApiClient not available');
        }

        console.log('ApiClient available, calling getPopularCategories(500)...');

        // Use same API as Popular Categories module
        // But get 500 categories instead of 100, and use all data instead of random selection
        let popularCategories;

        try {
            popularCategories = await window.ApiClient.getPopularCategories(500);
            console.log('🎯 API response received:', {
                type: typeof popularCategories,
                isArray: Array.isArray(popularCategories),
                length: popularCategories ? popularCategories.length : 'N/A',
                firstItem: popularCategories && popularCategories.length > 0 ? popularCategories[0] : 'N/A',
                lastItem: popularCategories && popularCategories.length > 0 ? popularCategories[popularCategories.length - 1] : 'N/A'
            });

            // Detailed data structure validation
            if (popularCategories && popularCategories.length > 0) {
                const sampleCategory = popularCategories[0];
                console.log('📊 Sample category structure:', {
                    id: sampleCategory.id,
                    name: sampleCategory.name,
                    count: sampleCategory.count,
                    quotesCount: sampleCategory.quotesCount,
                    allKeys: Object.keys(sampleCategory)
                });
            }
        } catch (apiError) {
            console.warn('❌ API call failed, using fallback data:', apiError);
            // Fallback to a smaller number if 500 fails
            try {
                popularCategories = await window.ApiClient.getPopularCategories(100);
                console.log('🔄 Fallback API response received:', popularCategories);
            } catch (fallbackError) {
                console.error('❌ Both API calls failed:', fallbackError);
                // Use mock data as last resort
                popularCategories = getMockCategories();
                console.log('🎭 Using mock data as fallback');
            }
        }

        if (!popularCategories || popularCategories.length === 0) {
            throw new Error('No categories returned from API or fallback');
        }

        // Store all categories (different from Popular Categories module which selects random 20)
        categoriesListPageState.allCategories = popularCategories;
        categoriesListPageState.totalCount = popularCategories.length;
        categoriesListPageState.loadedCount = popularCategories.length;

        console.log(`📦 Stored ${popularCategories.length} categories in page state`);
        console.log('🔍 Page state after storing:', {
            allCategoriesLength: categoriesListPageState.allCategories.length,
            totalCount: categoriesListPageState.totalCount,
            loadedCount: categoriesListPageState.loadedCount,
            firstCategory: categoriesListPageState.allCategories[0],
            pageSize: categoriesListPageState.pageSize,
            currentPage: categoriesListPageState.currentPage
        });

        // Cache to EntityIdMapper for performance optimization
        if (window.cachePopularEntities) {
            window.cachePopularEntities('category', popularCategories);
            console.log('Categories cached to EntityIdMapper');
        }

        // Apply initial sorting
        applySorting();
        console.log('Applied sorting');

        // Apply pagination
        applyPagination();
        console.log('Applied pagination');

        // Render categories
        renderCategories();
        console.log('Rendered categories');

        // Update statistics
        updateStats();
        console.log('Updated statistics');

        console.log(`Successfully loaded ${popularCategories.length} categories for list page`);

    } catch (error) {
        console.error('Error loading categories data:', error);
        throw error;
    }
}

/**
 * Initialize UI controls and event listeners
 */
function initializeControls() {
    // Search input
    const searchInput = document.getElementById('categories-search');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                handleSearch(e.target.value);
            }, 300); // Debounce search
        });
    }
    
    // Sort select
    const sortSelect = document.getElementById('sort-select');
    if (sortSelect) {
        sortSelect.addEventListener('change', (e) => {
            categoriesListPageState.sortOrder = e.target.value;
            applySorting();
            applyPagination();
            renderCategories();
        });
    }
    
    // View select
    const viewSelect = document.getElementById('view-select');
    if (viewSelect) {
        viewSelect.addEventListener('change', (e) => {
            categoriesListPageState.viewMode = e.target.value;
            renderCategories();
        });
    }
    
    // Refresh button
    const refreshBtn = document.getElementById('refresh-btn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', () => {
            location.reload();
        });
    }
    
    // Load more button
    const loadMoreBtn = document.getElementById('load-more-btn');
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', loadMoreCategories);
    }

    // Manual test button
    const testManualBtn = document.getElementById('test-manual-btn');
    if (testManualBtn) {
        testManualBtn.addEventListener('click', runManualTest);
    }
}

/**
 * Handle search functionality
 * Uses getCategories API for server-side search
 */
async function handleSearch(query) {
    categoriesListPageState.searchQuery = query.trim();
    
    if (!categoriesListPageState.searchQuery) {
        // Restore to original data
        categoriesListPageState.filteredCategories = categoriesListPageState.allCategories;
    } else {
        try {
            // Use API search (different from Popular Categories module)
            const searchResults = await window.ApiClient.getCategories(
                1, 100, categoriesListPageState.searchQuery
            );
            categoriesListPageState.filteredCategories = searchResults.categories || [];
        } catch (error) {
            console.error('Search error:', error);
            // Fallback to local search
            categoriesListPageState.filteredCategories = categoriesListPageState.allCategories.filter(
                category => category.name.toLowerCase().includes(categoriesListPageState.searchQuery.toLowerCase())
            );
        }
    }
    
    // Reset pagination and re-render
    categoriesListPageState.currentPage = 1;
    applyPagination();
    renderCategories();
    updateStats();
}

/**
 * Apply sorting to categories
 */
function applySorting() {
    const categories = categoriesListPageState.searchQuery ? 
        categoriesListPageState.filteredCategories : 
        categoriesListPageState.allCategories;
    
    switch (categoriesListPageState.sortOrder) {
        case 'alphabetical':
            categories.sort((a, b) => a.name.localeCompare(b.name));
            break;
        case 'count':
            categories.sort((a, b) => (b.count || 0) - (a.count || 0));
            break;
        case 'popularity':
        default:
            // Already sorted by popularity from API
            break;
    }
    
    if (categoriesListPageState.searchQuery) {
        categoriesListPageState.filteredCategories = categories;
    } else {
        categoriesListPageState.allCategories = categories;
    }
}

/**
 * Apply pagination
 */
function applyPagination() {
    const sourceCategories = categoriesListPageState.searchQuery ?
        categoriesListPageState.filteredCategories :
        categoriesListPageState.allCategories;

    console.log('📄 Applying pagination:', {
        searchQuery: categoriesListPageState.searchQuery,
        sourceCategoriesLength: sourceCategories.length,
        currentPage: categoriesListPageState.currentPage,
        pageSize: categoriesListPageState.pageSize
    });

    const startIndex = (categoriesListPageState.currentPage - 1) * categoriesListPageState.pageSize;
    const endIndex = startIndex + categoriesListPageState.pageSize;

    console.log('📄 Pagination calculation:', {
        startIndex,
        endIndex,
        sliceLength: endIndex - startIndex
    });

    categoriesListPageState.displayedCategories = sourceCategories.slice(startIndex, endIndex);
    categoriesListPageState.totalPages = Math.ceil(sourceCategories.length / categoriesListPageState.pageSize);

    console.log('📄 Pagination result:', {
        displayedCategoriesLength: categoriesListPageState.displayedCategories.length,
        totalPages: categoriesListPageState.totalPages,
        firstDisplayedCategory: categoriesListPageState.displayedCategories[0],
        lastDisplayedCategory: categoriesListPageState.displayedCategories[categoriesListPageState.displayedCategories.length - 1]
    });
}

/**
 * Render categories based on current view mode
 */
function renderCategories() {
    console.log('Rendering categories...');

    const container = document.getElementById('categories-container');
    if (!container) {
        console.error('Categories container not found - DOM elements:');
        console.log('Available elements with "container" in ID:',
            Array.from(document.querySelectorAll('[id*="container"]')).map(el => el.id));

        // Try to create the container if it doesn't exist
        const mainContent = document.querySelector('.main-content');
        if (mainContent) {
            console.log('Creating missing categories-container...');
            const newContainer = document.createElement('div');
            newContainer.id = 'categories-container';
            newContainer.className = 'categories-display';

            // Find the right place to insert it
            const loadingContainer = document.getElementById('loading-container');
            if (loadingContainer && loadingContainer.parentNode) {
                loadingContainer.parentNode.insertBefore(newContainer, loadingContainer.nextSibling);
            } else {
                mainContent.appendChild(newContainer);
            }

            // Retry with the new container
            return renderCategories();
        } else {
            console.error('Main content area not found either');
            return;
        }
    }

    console.log('✅ Categories container found, rendering...');

    // Debug current state before rendering
    console.log('🎨 Pre-render state:', {
        displayedCategoriesLength: categoriesListPageState.displayedCategories.length,
        viewMode: categoriesListPageState.viewMode,
        containerElement: container,
        containerClassName: container.className,
        containerInnerHTML: container.innerHTML.length + ' chars'
    });

    // Clear container
    container.innerHTML = '';
    console.log('🧹 Container cleared');

    if (categoriesListPageState.displayedCategories.length === 0) {
        console.log('❌ No categories to display, showing empty state');
        console.log('🔍 Debug empty state:', {
            allCategoriesLength: categoriesListPageState.allCategories.length,
            filteredCategoriesLength: categoriesListPageState.filteredCategories.length,
            searchQuery: categoriesListPageState.searchQuery,
            currentPage: categoriesListPageState.currentPage,
            pageSize: categoriesListPageState.pageSize
        });
        renderEmptyState(container);
        return;
    }

    console.log(`🎨 Rendering ${categoriesListPageState.displayedCategories.length} categories in ${categoriesListPageState.viewMode} mode`);
    console.log('🎨 Sample categories to render:', categoriesListPageState.displayedCategories.slice(0, 3));

    // Set container class based on view mode
    if (categoriesListPageState.viewMode === 'grid') {
        container.className = 'categories-display grid-view';
        console.log('🎨 Using grid view');
        renderGridView(container);
    } else {
        container.className = 'categories-display list-view';
        console.log('🎨 Using list view');
        renderListView(container);
    }

    // Verify rendering result
    console.log('✅ Categories rendered successfully');
    console.log('🎨 Post-render verification:', {
        containerChildren: container.children.length,
        containerInnerHTML: container.innerHTML.length + ' chars',
        firstChild: container.firstElementChild ? container.firstElementChild.tagName : 'none',
        lastChild: container.lastElementChild ? container.lastElementChild.tagName : 'none'
    });
}

/**
 * Render grid view
 */
function renderGridView(container) {
    console.log('🎯 Rendering grid view with', categoriesListPageState.displayedCategories.length, 'categories');

    categoriesListPageState.displayedCategories.forEach((category, index) => {
        console.log(`🎯 Rendering category ${index + 1}:`, {
            id: category.id,
            name: category.name,
            count: category.count,
            quotesCount: category.quotesCount
        });

        const categoryCard = document.createElement('a');

        // Check if UrlHandler is available
        if (window.UrlHandler && window.UrlHandler.getCategoryUrl) {
            categoryCard.href = window.UrlHandler.getCategoryUrl(category);
        } else {
            console.warn('UrlHandler not available, using fallback URL');
            categoryCard.href = `/categories/${category.name.toLowerCase().replace(/\s+/g, '-')}/`;
        }

        categoryCard.className = 'category-card';
        categoryCard.style.animationDelay = `${index * 0.1}s`;

        // Use optimized navigation
        categoryCard.addEventListener('click', (e) => {
            e.preventDefault();
            navigateWithOptimization('category', category.id, category.name);
        });

        categoryCard.innerHTML = `
            <div class="category-icon">
                <i class="fas fa-tag"></i>
            </div>
            <h3 class="category-title">${category.name}</h3>
            <p class="category-count">${category.count || 0} quotes</p>
        `;

        container.appendChild(categoryCard);
        console.log(`✅ Category card ${index + 1} added to container`);
    });

    console.log('🎯 Grid view rendering complete. Container now has', container.children.length, 'children');
}

/**
 * Render list view
 */
function renderListView(container) {
    categoriesListPageState.displayedCategories.forEach((category, index) => {
        const categoryItem = document.createElement('a');

        // Generate proper URL
        if (window.UrlHandler && window.UrlHandler.getCategoryUrl) {
            categoryItem.href = window.UrlHandler.getCategoryUrl(category);
        } else {
            const slug = category.name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9\-]/g, '');
            categoryItem.href = `/categories/${slug}/`;
        }

        categoryItem.className = 'category-item';
        categoryItem.style.animationDelay = `${index * 0.05}s`;

        // Use optimized navigation
        categoryItem.addEventListener('click', (e) => {
            e.preventDefault();
            navigateWithOptimization('category', category.id, category.name);
        });
        
        categoryItem.innerHTML = `
            <div class="category-info">
                <div class="category-icon">
                    <i class="fas fa-tag"></i>
                </div>
                <h3 class="category-title">${category.name}</h3>
            </div>
            <div class="category-count">${category.count || 0} quotes</div>
        `;
        
        container.appendChild(categoryItem);
    });
}

/**
 * Render empty state
 */
function renderEmptyState(container) {
    const emptyMessage = categoriesListPageState.searchQuery ? 
        'No categories found for your search.' : 
        'No categories available.';
    
    container.innerHTML = `
        <div class="text-center py-12">
            <i class="fas fa-search text-4xl text-gray-300 dark:text-gray-600 mb-4"></i>
            <h3 class="text-lg font-semibold mb-2 text-gray-900 dark:text-gray-100">${emptyMessage}</h3>
            <p class="text-gray-500 dark:text-gray-400 mb-4">Try adjusting your search terms or browse all categories.</p>
            ${categoriesListPageState.searchQuery ? 
                '<button onclick="clearSearch()" class="btn-primary">Clear Search</button>' : 
                '<button onclick="location.reload()" class="btn-primary">Refresh Page</button>'
            }
        </div>
    `;
}

/**
 * Clear search
 */
function clearSearch() {
    const searchInput = document.getElementById('categories-search');
    if (searchInput) {
        searchInput.value = '';
        handleSearch('');
    }
}

/**
 * Navigate with optimization (EntityIdMapper)
 */
function navigateWithOptimization(type, entityId, entityName) {
    console.log(`🚀 navigateWithOptimization called:`, {
        type, entityId, entityName,
        hasUrlHandler: !!window.UrlHandler,
        hasGetCategoryUrl: !!(window.UrlHandler && window.UrlHandler.getCategoryUrl),
        urlHandlerType: window.UrlHandler ? typeof window.UrlHandler.getCategoryUrl : 'undefined'
    });

    if (window.setOptimizedNavigationData) {
        window.setOptimizedNavigationData({
            entityType: type,
            entityId: entityId,
            entityName: entityName
        });
    }

    // Generate proper semantic URL for category
    let url;
    if (window.UrlHandler && window.UrlHandler.getCategoryUrl) {
        try {
            url = window.UrlHandler.getCategoryUrl({id: entityId, name: entityName});
            console.log(`✅ Generated URL using new UrlHandler: ${url}`);
        } catch (error) {
            console.error(`❌ Error using new UrlHandler:`, error);
            // Fallback URL generation
            const slug = entityName.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9\-]/g, '');
            url = `/categories/${slug}/`;
            console.log(`🔄 Using fallback URL: ${url}`);
        }
    } else {
        // Fallback URL generation
        const slug = entityName.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9\-]/g, '');
        url = `/categories/${slug}/`;
        console.log(`🔄 Using fallback URL (no UrlHandler): ${url}`);
    }

    console.log(`🔗 Final navigation: ${entityName} -> ${url}`);
    window.location.href = url;
}

/**
 * Update statistics display
 */
function updateStats() {
    const showingCountEl = document.getElementById('showing-count');
    const totalCountEl = document.getElementById('total-count');
    
    if (showingCountEl) {
        showingCountEl.textContent = categoriesListPageState.displayedCategories.length;
    }
    
    if (totalCountEl) {
        const total = categoriesListPageState.searchQuery ? 
            categoriesListPageState.filteredCategories.length : 
            categoriesListPageState.totalCount;
        totalCountEl.textContent = total;
    }
}

/**
 * Show loading state
 */
function showLoadingState() {
    categoriesListPageState.isLoading = true;

    const loadingContainer = document.getElementById('loading-container');
    const categoriesContainer = document.getElementById('categories-container');
    const errorContainer = document.getElementById('error-container');

    console.log('Showing loading state...');
    console.log('Loading container:', loadingContainer);
    console.log('Categories container:', categoriesContainer);
    console.log('Error container:', errorContainer);

    if (loadingContainer) {
        loadingContainer.style.display = 'block';
        console.log('Loading container shown');
    }
    if (categoriesContainer) {
        categoriesContainer.style.display = 'none';
        console.log('Categories container hidden');
    }
    if (errorContainer) {
        errorContainer.style.display = 'none';
        console.log('Error container hidden');
    }
}

/**
 * Hide loading state
 */
function hideLoadingState() {
    categoriesListPageState.isLoading = false;
    
    const loadingContainer = document.getElementById('loading-container');
    const categoriesContainer = document.getElementById('categories-container');
    
    if (loadingContainer) loadingContainer.style.display = 'none';
    if (categoriesContainer) categoriesContainer.style.display = 'block';
}

/**
 * Show error state
 */
function showErrorState(error) {
    const loadingContainer = document.getElementById('loading-container');
    const categoriesContainer = document.getElementById('categories-container');
    const errorContainer = document.getElementById('error-container');
    
    if (loadingContainer) loadingContainer.style.display = 'none';
    if (categoriesContainer) categoriesContainer.style.display = 'none';
    if (errorContainer) errorContainer.style.display = 'block';
    
    console.error('Categories page error:', error);
}

/**
 * Load sidebar content
 */
async function loadSidebarContent() {
    try {
        // Load popular authors for sidebar
        const popularAuthors = await window.ApiClient.getPopularAuthors(5);
        renderSidebarAuthors(popularAuthors);
        
        // Load popular sources for sidebar
        const popularSources = await window.ApiClient.getPopularSources(5);
        renderSidebarSources(popularSources);
    } catch (error) {
        console.warn('Failed to load sidebar content:', error);
    }
}

/**
 * Render sidebar authors
 */
function renderSidebarAuthors(authors) {
    const container = document.getElementById('popular-authors-sidebar');
    if (!container || !authors) return;
    
    container.innerHTML = authors.map(author => `
        <div class="flex items-center py-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded">
            <div class="w-8 h-8 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center mr-3">
                <i class="fas fa-user text-white text-sm"></i>
            </div>
            <div class="flex-1">
                <a href="${window.UrlHandler.getAuthorUrl(author)}" class="text-sm font-medium hover:text-blue-600 dark:hover:text-blue-400">
                    ${author.name}
                </a>
                <p class="text-xs text-gray-500 dark:text-gray-400">${author.count || 0} quotes</p>
            </div>
        </div>
    `).join('');
}

/**
 * Render sidebar sources
 */
function renderSidebarSources(sources) {
    const container = document.getElementById('popular-sources-sidebar');
    if (!container || !sources) return;
    
    container.innerHTML = sources.map(source => `
        <div class="flex items-center py-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded">
            <div class="w-8 h-8 bg-gradient-to-br from-green-400 to-blue-500 rounded-full flex items-center justify-center mr-3">
                <i class="fas fa-book text-white text-sm"></i>
            </div>
            <div class="flex-1">
                <a href="${window.UrlHandler.getSourceUrl(source)}" class="text-sm font-medium hover:text-blue-600 dark:hover:text-blue-400">
                    ${source.name}
                </a>
                <p class="text-xs text-gray-500 dark:text-gray-400">${source.count || 0} quotes</p>
            </div>
        </div>
    `).join('');
}

/**
 * Get mock categories data for fallback
 */
function getMockCategories() {
    return [
        { id: 1, name: 'Life', count: 150 },
        { id: 2, name: 'Love', count: 120 },
        { id: 3, name: 'Success', count: 100 },
        { id: 4, name: 'Wisdom', count: 95 },
        { id: 5, name: 'Motivation', count: 90 },
        { id: 6, name: 'Happiness', count: 85 },
        { id: 7, name: 'Inspiration', count: 80 },
        { id: 8, name: 'Philosophy', count: 75 },
        { id: 9, name: 'Truth', count: 70 },
        { id: 10, name: 'Friendship', count: 65 },
        { id: 11, name: 'Hope', count: 60 },
        { id: 12, name: 'Dreams', count: 55 },
        { id: 13, name: 'Courage', count: 50 },
        { id: 14, name: 'Peace', count: 45 },
        { id: 15, name: 'Faith', count: 40 },
        { id: 16, name: 'Knowledge', count: 35 },
        { id: 17, name: 'Freedom', count: 30 },
        { id: 18, name: 'Beauty', count: 25 },
        { id: 19, name: 'Time', count: 20 },
        { id: 20, name: 'Change', count: 15 }
    ];
}

/**
 * Run manual test to verify functionality
 */
async function runManualTest() {
    console.log('🧪 Running manual test...');

    try {
        // Hide loading and error states
        hideLoadingState();
        const errorContainer = document.getElementById('error-container');
        if (errorContainer) errorContainer.style.display = 'none';

        // Test 1: Use mock data
        console.log('🧪 Test 1: Using mock data');
        const mockCategories = getMockCategories();

        // Store in state
        categoriesListPageState.allCategories = mockCategories;
        categoriesListPageState.totalCount = mockCategories.length;
        categoriesListPageState.loadedCount = mockCategories.length;
        categoriesListPageState.filteredCategories = [];
        categoriesListPageState.searchQuery = '';

        console.log('🧪 Mock data stored in state:', {
            allCategoriesLength: categoriesListPageState.allCategories.length,
            totalCount: categoriesListPageState.totalCount
        });

        // Apply pagination
        applyPagination();
        console.log('🧪 Pagination applied:', {
            displayedCategoriesLength: categoriesListPageState.displayedCategories.length
        });

        // Render categories
        renderCategories();
        console.log('🧪 Categories rendered');

        // Update stats
        updateStats();
        console.log('🧪 Stats updated');

        console.log('✅ Manual test completed successfully with mock data');

        // Test 2: Try real API call
        setTimeout(async () => {
            console.log('🧪 Test 2: Trying real API call...');
            try {
                if (window.ApiClient) {
                    const realCategories = await window.ApiClient.getPopularCategories(50);
                    if (realCategories && realCategories.length > 0) {
                        console.log('🧪 Real API data received:', realCategories.length, 'categories');

                        // Update with real data
                        categoriesListPageState.allCategories = realCategories;
                        categoriesListPageState.totalCount = realCategories.length;
                        categoriesListPageState.loadedCount = realCategories.length;

                        applyPagination();
                        renderCategories();
                        updateStats();

                        console.log('✅ Manual test completed successfully with real API data');
                    } else {
                        console.log('⚠️ Real API returned no data, keeping mock data');
                    }
                } else {
                    console.log('⚠️ ApiClient not available, keeping mock data');
                }
            } catch (apiError) {
                console.log('⚠️ Real API call failed, keeping mock data:', apiError.message);
            }
        }, 2000);

    } catch (error) {
        console.error('❌ Manual test failed:', error);
        showErrorState(error);
    }
}

// Expose functions to global scope for PageRouter
window.initCategoriesListPage = initCategoriesListPage;

// Add immediate console log to verify script loading
console.log('✅ Categories.js script loaded successfully');
console.log('✅ initCategoriesListPage function exposed to global scope');

// Test function to verify everything is working
window.testCategoriesPage = function() {
    console.log('🧪 Testing Categories Page...');
    console.log('ApiClient available:', !!window.ApiClient);
    console.log('PageRouter available:', !!window.PageRouter);
    console.log('ComponentLoader available:', !!window.ComponentLoader);
    console.log('UrlHandler available:', !!window.UrlHandler);
    console.log('Current page type:', window.UrlHandler ? window.UrlHandler.getCurrentPageType() : 'UrlHandler not available');

    // Test basic DOM elements
    const elements = [
        'loading-container',
        'categories-container',
        'error-container',
        'categories-search',
        'sort-select',
        'view-select'
    ];

    elements.forEach(id => {
        const element = document.getElementById(id);
        console.log(`Element ${id}:`, !!element);
    });

    // Test API call
    if (window.ApiClient) {
        console.log('🔍 Testing API call...');
        window.ApiClient.getPopularCategories(10).then(categories => {
            console.log('✅ API test successful:', categories);
        }).catch(error => {
            console.error('❌ API test failed:', error);
        });
    }

    // Test manual initialization
    console.log('🚀 Testing manual initialization...');
    if (window.initCategoriesListPage) {
        window.initCategoriesListPage({}).then(() => {
            console.log('✅ Manual initialization successful');
        }).catch(error => {
            console.error('❌ Manual initialization failed:', error);
        });
    }
};
