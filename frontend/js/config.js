/**
 * 网站配置
 * 包含不同环境下的配置参数
 */

const Config = {
    // 开发环境配置 - 使用本地API端点
    development: {
        apiEndpoint: 'http://127.0.0.1:8000/api/',      // REST API端点
        graphqlEndpoint: 'http://127.0.0.1:8000/graphql/', // GraphQL API端点
        useMockData: false,  // 禁用模拟数据，使用真实API
        debug: true
    },

    // 测试环境配置
    testing: {
        apiEndpoint: 'http://43.153.11.77:8000/api/',
        graphqlEndpoint: 'http://43.153.11.77:8000/graphql/',
        useMockData: false,  // 测试环境也使用生产API数据
        debug: true
    },

    // 生产环境配置
    production: {
        apiEndpoint: 'https://api.quotese.com/api/',
        graphqlEndpoint: 'https://api.quotese.com/graphql/',
        useMockData: false,
        debug: false
    },

    // 获取当前环境配置
    // 可以根据域名或其他条件判断当前环境
    getCurrent: function() {
  
            return this.production;
    }
};

// 导出当前环境配置
window.AppConfig = Config.getCurrent();
