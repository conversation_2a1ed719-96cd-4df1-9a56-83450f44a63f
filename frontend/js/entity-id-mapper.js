/**
 * 统一实体ID映射管理器
 * 为Categories、Authors、Sources提供统一的ID映射和查找服务
 * 
 * @version 1.0.0
 * @date 2025-06-16
 * <AUTHOR>
 */

// 统一的实体ID映射配置
const KNOWN_ENTITY_IDS = {
    categories: {
        'life': 71523,
        'writing': 142145,
        'friendship': null,    // 待确认
        'wisdom': null,        // 待确认
        'love': null,          // 待确认
        'success': null,       // 待确认
        'motivation': null,    // 待确认
        'happiness': null,     // 待确认
        'inspiration': null,   // 待确认
        'leadership': null,    // 待确认
        'business': null,      // 待确认
        'education': null,     // 待确认
        'family': null,        // 待确认
        'health': null,        // 待确认
        'creativity': null,    // 待确认
        'courage': null,       // 待确认
        'perseverance': null   // 待确认
    },
    authors: {
        'albert-einstein': 2013,  // 已知
        'steve-jobs': null,       // 待确认
        'pearl-zhu': null,        // 待确认
        'mark-twain': null,       // 待确认
        'oscar-wilde': null,      // 待确认
        'winston-churchill': null, // 待确认
        'maya-angelou': null,     // 待确认
        'nelson-mandela': null,   // 待确认
        'martin-luther-king': null, // 待确认
        'gandhi': null,           // 待确认
        'aristotle': null,        // 待确认
        'plato': null,            // 待确认
        'confucius': null,        // 待确认
        'buddha': null            // 待确认
    },
    sources: {
        'meditations': null,      // 待确认
        'healology': null,        // 待确认
        'interview': null,        // 待确认
        'speech': null,           // 待确认
        'letter': null,           // 待确认
        'book': null,             // 待确认
        'article': null,          // 待确认
        'essay': null,            // 待确认
        'diary': null,            // 待确认
        'autobiography': null,    // 待确认
        'biography': null,        // 待确认
        'novel': null,            // 待确认
        'poem': null,             // 待确认
        // 长slug来源
        'the-9-cardinal-building-blocks-for-continued-success-in-leadership': null, // 待确认
        'rise-up-and-salute-the-sun-the-writings-of-suzy-kassem': null,            // 待确认
        'his-final-gift': null,   // 待确认
        'the-art-of-action-8-ways-to-initiate-activate-forward-momentum-for-positive-impact': null, // 待确认
        'from-within-i-rise-spiritual-triumph-over-death-and-conscious-encounters-with-the-divine-presence': null // 待确认
    }
};

/**
 * 实体ID映射管理器类
 */
class EntityIdMapper {
    constructor() {
        this.mappings = KNOWN_ENTITY_IDS;
        this.stats = {
            hits: 0,
            misses: 0,
            apiQueries: 0
        };
    }

    /**
     * 获取已知实体ID
     * @param {string} entityType - 实体类型 (categories, authors, sources)
     * @param {string} slug - 实体slug
     * @returns {number|null} - 实体ID或null
     */
    getKnownId(entityType, slug) {
        const normalizedSlug = slug.toLowerCase();
        const id = this.mappings[entityType]?.[normalizedSlug];
        
        if (id !== undefined && id !== null) {
            this.stats.hits++;
            console.log(`✅ EntityIdMapper: Found known ID for ${entityType}/${normalizedSlug}: ${id}`);
            return id;
        } else {
            this.stats.misses++;
            console.log(`❌ EntityIdMapper: No known ID for ${entityType}/${normalizedSlug}`);
            return null;
        }
    }

    /**
     * 添加新的实体ID映射
     * @param {string} entityType - 实体类型
     * @param {string} slug - 实体slug
     * @param {number} id - 实体ID
     */
    addMapping(entityType, slug, id) {
        if (!this.mappings[entityType]) {
            this.mappings[entityType] = {};
        }
        
        const normalizedSlug = slug.toLowerCase();
        this.mappings[entityType][normalizedSlug] = id;
        
        console.log(`📝 EntityIdMapper: Added mapping ${entityType}/${normalizedSlug} → ${id}`);
    }

    /**
     * 批量更新实体ID映射
     * @param {string} entityType - 实体类型
     * @param {Object} mappings - 映射对象
     */
    updateMappings(entityType, mappings) {
        if (!this.mappings[entityType]) {
            this.mappings[entityType] = {};
        }
        
        this.mappings[entityType] = { ...this.mappings[entityType], ...mappings };
        
        console.log(`📝 EntityIdMapper: Updated ${Object.keys(mappings).length} mappings for ${entityType}`);
    }

    /**
     * 获取统计信息
     * @returns {Object} - 统计信息
     */
    getStats() {
        const total = this.stats.hits + this.stats.misses;
        const hitRate = total > 0 ? Math.round((this.stats.hits / total) * 100) : 0;
        
        return {
            ...this.stats,
            total,
            hitRate: `${hitRate}%`
        };
    }

    /**
     * 重置统计信息
     */
    resetStats() {
        this.stats = {
            hits: 0,
            misses: 0,
            apiQueries: 0
        };
    }

    /**
     * 获取实体类型的所有映射
     * @param {string} entityType - 实体类型
     * @returns {Object} - 映射对象
     */
    getMappings(entityType) {
        return this.mappings[entityType] || {};
    }

    /**
     * 获取实体类型的映射统计
     * @param {string} entityType - 实体类型
     * @returns {Object} - 映射统计
     */
    getMappingStats(entityType) {
        const mappings = this.getMappings(entityType);
        const total = Object.keys(mappings).length;
        const found = Object.values(mappings).filter(v => v !== null).length;
        const coverage = total > 0 ? Math.round((found / total) * 100) : 0;
        
        return {
            total,
            found,
            missing: total - found,
            coverage: `${coverage}%`
        };
    }

    /**
     * 导出映射表为代码格式
     * @param {string} entityType - 实体类型
     * @returns {string} - 代码字符串
     */
    exportMappingsAsCode(entityType) {
        const mappings = this.getMappings(entityType);
        const stats = this.getMappingStats(entityType);
        
        let code = `// ${entityType.toUpperCase()} ID映射表\n`;
        code += `// 统计: ${stats.found}/${stats.total} 个实体有ID (${stats.coverage})\n`;
        code += `const KNOWN_${entityType.toUpperCase()}_IDS = {\n`;
        
        for (const [key, value] of Object.entries(mappings)) {
            if (value !== null) {
                code += `    '${key}': ${value},\n`;
            } else {
                code += `    '${key}': null, // 待确认\n`;
            }
        }
        
        code += `};\n`;
        
        return code;
    }

    /**
     * 记录API查询统计
     */
    recordApiQuery() {
        this.stats.apiQueries++;
    }

    /**
     * 从智能缓存同步数据到映射表
     * @param {string} cacheKey - 缓存键 (categories, authors, sources)
     */
    syncFromSmartCache(cacheKey) {
        if (!window.entityCache || !window.entityCache[cacheKey]) {
            console.log(`📋 EntityIdMapper: No smart cache found for ${cacheKey}`);
            return;
        }

        const cache = window.entityCache[cacheKey];
        let syncedCount = 0;

        for (const [id, cachedEntity] of cache.entries()) {
            if (cachedEntity.slug) {
                this.addMapping(cacheKey, cachedEntity.slug, cachedEntity.id);
                syncedCount++;
            }
        }

        console.log(`🔄 EntityIdMapper: Synced ${syncedCount} entities from smart cache for ${cacheKey}`);
    }

    /**
     * 从所有智能缓存同步数据
     */
    syncAllFromSmartCache() {
        ['categories', 'authors', 'sources'].forEach(cacheKey => {
            this.syncFromSmartCache(cacheKey);
        });
    }

    /**
     * 获取缓存同步统计
     * @returns {Object} - 缓存同步统计
     */
    getCacheSyncStats() {
        if (!window.entityCache) {
            return { available: false };
        }

        const stats = {
            available: true,
            categories: window.entityCache.categories.size,
            authors: window.entityCache.authors.size,
            sources: window.entityCache.sources.size,
            lastUpdated: window.entityCache.lastUpdated
        };

        stats.total = stats.categories + stats.authors + stats.sources;
        return stats;
    }
}

/**
 * 通用的优先级实体查找函数
 * @param {string} entityType - 实体类型 (categories, authors, sources)
 * @param {string} slug - 实体slug
 * @param {string} name - 实体名称
 * @param {Function} apiMethod - API查询方法
 * @returns {Promise<Object|null>} - 实体对象或null
 */
async function findEntityWithPriority(entityType, slug, name, apiMethod) {
    // 1. 优先级1：已知ID映射表
    const knownId = window.EntityIdMapper.getKnownId(entityType, slug);
    if (knownId) {
        console.log(`🚀 EntityIdMapper: Using known ID for ${entityType} "${name}":`, knownId);
        return {
            id: knownId,
            name: name,
            fromCache: true,
            slug: slug
        };
    }

    // 2. 优先级2：智能缓存检查（来自热门模块数据）
    if (window.getCachedEntityData) {
        const cache = window.entityCache && window.entityCache[entityType];
        if (cache) {
            for (const [id, cachedEntity] of cache.entries()) {
                if (cachedEntity.name === name ||
                    cachedEntity.slug === slug ||
                    window.UrlHandler.slugify(cachedEntity.name) === slug) {

                    console.log(`🚀 EntityIdMapper: Found in smart cache for ${entityType} "${name}":`, cachedEntity);

                    // 自动添加到映射表以供将来使用
                    window.EntityIdMapper.addMapping(entityType, slug, cachedEntity.id);

                    return {
                        id: cachedEntity.id,
                        name: cachedEntity.name,
                        count: cachedEntity.count,
                        fromCache: true,
                        fromSmartCache: true,
                        slug: slug
                    };
                }
            }
        }
    }

    // 3. 优先级3-5：API查询fallback
    const queries = [slug, name, name.toLowerCase()];
    
    for (const query of queries) {
        try {
            window.EntityIdMapper.recordApiQuery();
            console.log(`🔍 EntityIdMapper: Trying API query for ${entityType} with: "${query}"`);
            
            const result = await apiMethod(query);
            if (result) {
                console.log(`✅ EntityIdMapper: Found ${entityType} via API:`, result);
                
                // 自动添加到映射表以供将来使用
                window.EntityIdMapper.addMapping(entityType, slug, result.id);
                
                return result;
            }
        } catch (error) {
            console.warn(`❌ EntityIdMapper: API query failed for ${entityType}/${query}:`, error);
        }
    }
    
    console.error(`❌ EntityIdMapper: All queries failed for ${entityType}/${slug}`);
    return null;
}

// 创建全局实例
window.EntityIdMapper = new EntityIdMapper();

// 导出通用查找函数
window.findEntityWithPriority = findEntityWithPriority;

console.log('📋 EntityIdMapper initialized with mappings:', window.EntityIdMapper.mappings);
