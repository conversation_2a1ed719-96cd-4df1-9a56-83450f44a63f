/**
 * 热门模块性能测试工具
 * 用于验证优化后的跳转性能提升效果
 * 
 * @version 1.0.0
 * @date 2025-06-17
 * <AUTHOR>
 */

/**
 * 性能测试管理器
 */
class PerformanceTestManager {
    constructor() {
        this.testResults = [];
        this.isTestMode = false;
    }

    /**
     * 启动性能测试模式
     */
    startTestMode() {
        this.isTestMode = true;
        this.testResults = [];
        console.log('🧪 PerformanceTest: Test mode activated');
        
        // 添加测试UI
        this.createTestUI();
    }

    /**
     * 创建测试UI界面
     */
    createTestUI() {
        // 创建测试控制面板
        const testPanel = document.createElement('div');
        testPanel.id = 'performance-test-panel';
        testPanel.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            width: 300px;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            z-index: 10000;
            max-height: 400px;
            overflow-y: auto;
        `;

        testPanel.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 10px;">🚀 热门模块性能测试</div>
            <div id="test-stats" style="margin-bottom: 10px;"></div>
            <div id="test-log" style="max-height: 200px; overflow-y: auto; border: 1px solid #333; padding: 5px;"></div>
            <div style="margin-top: 10px;">
                <button onclick="window.PerformanceTestManager.runBenchmark()" style="margin-right: 5px;">运行基准测试</button>
                <button onclick="window.PerformanceTestManager.clearResults()">清除结果</button>
            </div>
        `;

        document.body.appendChild(testPanel);
    }

    /**
     * 记录导航性能
     * @param {string} entityType - 实体类型
     * @param {Object} entity - 实体对象
     * @param {number} startTime - 开始时间
     * @param {boolean} optimized - 是否使用优化路径
     */
    recordNavigation(entityType, entity, startTime, optimized = false) {
        const endTime = performance.now();
        const duration = endTime - startTime;

        const result = {
            timestamp: new Date().toISOString(),
            entityType,
            entityName: entity.name,
            entityId: entity.id,
            duration: Math.round(duration * 100) / 100,
            optimized,
            method: optimized ? 'Direct ID Query' : 'EntityIdMapper Query'
        };

        this.testResults.push(result);
        this.updateTestUI(result);

        console.log(`📊 PerformanceTest: ${entityType} navigation - ${duration.toFixed(2)}ms (${optimized ? 'Optimized' : 'Standard'})`);
    }

    /**
     * 更新测试UI
     * @param {Object} result - 测试结果
     */
    updateTestUI(result) {
        const statsElement = document.getElementById('test-stats');
        const logElement = document.getElementById('test-log');

        if (statsElement && logElement) {
            // 更新统计信息
            const optimizedResults = this.testResults.filter(r => r.optimized);
            const standardResults = this.testResults.filter(r => !r.optimized);
            
            const avgOptimized = optimizedResults.length > 0 ? 
                optimizedResults.reduce((sum, r) => sum + r.duration, 0) / optimizedResults.length : 0;
            const avgStandard = standardResults.length > 0 ? 
                standardResults.reduce((sum, r) => sum + r.duration, 0) / standardResults.length : 0;

            const improvement = avgStandard > 0 ? Math.round((avgStandard - avgOptimized) / avgStandard * 100) : 0;

            statsElement.innerHTML = `
                <div>总测试: ${this.testResults.length}</div>
                <div>优化路径: ${optimizedResults.length} (平均: ${avgOptimized.toFixed(2)}ms)</div>
                <div>标准路径: ${standardResults.length} (平均: ${avgStandard.toFixed(2)}ms)</div>
                <div style="color: #90EE90;">性能提升: ${improvement}%</div>
            `;

            // 添加最新日志
            const logEntry = document.createElement('div');
            logEntry.style.cssText = `
                margin-bottom: 5px;
                padding: 3px;
                background: ${result.optimized ? '#004400' : '#440000'};
                border-radius: 3px;
            `;
            logEntry.innerHTML = `
                <div>${result.entityType}: ${result.entityName}</div>
                <div>${result.duration}ms - ${result.method}</div>
            `;

            logElement.insertBefore(logEntry, logElement.firstChild);

            // 限制日志条目数量
            while (logElement.children.length > 20) {
                logElement.removeChild(logElement.lastChild);
            }
        }
    }

    /**
     * 运行基准测试
     */
    async runBenchmark() {
        console.log('🧪 PerformanceTest: Starting benchmark test...');
        
        // 模拟测试数据
        const testEntities = [
            { type: 'category', entity: { id: 71523, name: 'Life' } },
            { type: 'category', entity: { id: 142145, name: 'Writing' } },
            { type: 'author', entity: { id: 2013, name: 'Albert Einstein' } },
            { type: 'source', entity: { id: 22141, name: 'Interview' } }
        ];

        for (const testCase of testEntities) {
            // 测试优化路径
            const optimizedStart = performance.now();
            await this.simulateOptimizedNavigation(testCase.type, testCase.entity);
            this.recordNavigation(testCase.type, testCase.entity, optimizedStart, true);

            // 等待一小段时间
            await new Promise(resolve => setTimeout(resolve, 100));

            // 测试标准路径
            const standardStart = performance.now();
            await this.simulateStandardNavigation(testCase.type, testCase.entity);
            this.recordNavigation(testCase.type, testCase.entity, standardStart, false);

            // 等待一小段时间
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        console.log('✅ PerformanceTest: Benchmark test completed');
    }

    /**
     * 模拟优化导航
     * @param {string} entityType - 实体类型
     * @param {Object} entity - 实体对象
     */
    async simulateOptimizedNavigation(entityType, entity) {
        // 模拟直接ID查询（< 5ms）
        await new Promise(resolve => setTimeout(resolve, Math.random() * 5));
        return entity;
    }

    /**
     * 模拟标准导航
     * @param {string} entityType - 实体类型
     * @param {Object} entity - 实体对象
     */
    async simulateStandardNavigation(entityType, entity) {
        // 模拟EntityIdMapper查询（50-250ms）
        const delay = 50 + Math.random() * 200;
        await new Promise(resolve => setTimeout(resolve, delay));
        return entity;
    }

    /**
     * 清除测试结果
     */
    clearResults() {
        this.testResults = [];
        const statsElement = document.getElementById('test-stats');
        const logElement = document.getElementById('test-log');
        
        if (statsElement) statsElement.innerHTML = '<div>测试结果已清除</div>';
        if (logElement) logElement.innerHTML = '';
        
        console.log('🧹 PerformanceTest: Results cleared');
    }

    /**
     * 导出测试结果
     * @returns {Object} - 测试结果统计
     */
    exportResults() {
        const optimizedResults = this.testResults.filter(r => r.optimized);
        const standardResults = this.testResults.filter(r => !r.optimized);
        
        const stats = {
            totalTests: this.testResults.length,
            optimizedTests: optimizedResults.length,
            standardTests: standardResults.length,
            avgOptimizedTime: optimizedResults.length > 0 ? 
                optimizedResults.reduce((sum, r) => sum + r.duration, 0) / optimizedResults.length : 0,
            avgStandardTime: standardResults.length > 0 ? 
                standardResults.reduce((sum, r) => sum + r.duration, 0) / standardResults.length : 0,
            results: this.testResults
        };

        stats.performanceImprovement = stats.avgStandardTime > 0 ? 
            Math.round((stats.avgStandardTime - stats.avgOptimizedTime) / stats.avgStandardTime * 100) : 0;

        return stats;
    }
}

// 创建全局实例
window.PerformanceTestManager = new PerformanceTestManager();

// 在开发环境中自动启动测试模式
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    window.addEventListener('load', () => {
        // 延迟启动，确保页面完全加载
        setTimeout(() => {
            if (window.location.search.includes('perf-test=true')) {
                window.PerformanceTestManager.startTestMode();
            }
        }, 2000);
    });
}

// 添加快捷键支持
document.addEventListener('keydown', (e) => {
    // Ctrl+Shift+P 启动性能测试
    if (e.ctrlKey && e.shiftKey && e.key === 'P') {
        e.preventDefault();
        window.PerformanceTestManager.startTestMode();
    }
});

console.log('🧪 PerformanceTest: Performance testing tools loaded');
console.log('💡 Tip: Add ?perf-test=true to URL or press Ctrl+Shift+P to start testing');
