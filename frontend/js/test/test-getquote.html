<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>getQuote API Method Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-case {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-case h4 {
            margin-top: 0;
            color: #555;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .loading {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">getQuote API Method Test Suite</h1>
        <p>This page tests the newly implemented getQuote method in ApiClient.</p>
        
        <div class="test-case">
            <h4>Test 1: Valid Quote ID (ID: 1)</h4>
            <button class="test-button" onclick="testValidQuoteId()">Run Test</button>
            <div id="test1-result" class="test-result"></div>
        </div>

        <div class="test-case">
            <h4>Test 2: Invalid Quote ID (ID: -1)</h4>
            <button class="test-button" onclick="testInvalidQuoteId()">Run Test</button>
            <div id="test2-result" class="test-result"></div>
        </div>

        <div class="test-case">
            <h4>Test 3: Non-existent Quote ID (ID: 999999)</h4>
            <button class="test-button" onclick="testNonExistentQuoteId()">Run Test</button>
            <div id="test3-result" class="test-result"></div>
        </div>

        <div class="test-case">
            <h4>Test 4: String Quote ID (ID: "abc")</h4>
            <button class="test-button" onclick="testStringQuoteId()">Run Test</button>
            <div id="test4-result" class="test-result"></div>
        </div>

        <div class="test-case">
            <h4>Test 5: Null/Undefined Quote ID</h4>
            <button class="test-button" onclick="testNullQuoteId()">Run Test</button>
            <div id="test5-result" class="test-result"></div>
        </div>

        <div class="test-case">
            <h4>Test 6: Cache Functionality</h4>
            <button class="test-button" onclick="testCacheFunctionality()">Run Test</button>
            <div id="test6-result" class="test-result"></div>
        </div>

        <div class="test-case">
            <h4>Test 7: Multiple Valid IDs</h4>
            <button class="test-button" onclick="testMultipleValidIds()">Run Test</button>
            <div id="test7-result" class="test-result"></div>
        </div>

        <div class="test-case">
            <h4>Run All Tests</h4>
            <button class="test-button" onclick="runAllTests()" style="background: #28a745;">Run All Tests</button>
            <div id="all-tests-result" class="test-result"></div>
        </div>
    </div>

    <!-- Load required scripts -->
    <script src="../config.js"></script>
    <script src="../mock-data.js"></script>
    <script src="../api-client.js"></script>

    <script>
        // Test utility functions
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `test-result ${type}`;
        }

        function showLoading(elementId) {
            showResult(elementId, 'Running test...', 'loading');
        }

        // Test 1: Valid Quote ID
        async function testValidQuoteId() {
            showLoading('test1-result');
            try {
                const quote = await window.ApiClient.getQuote(1);
                if (quote && quote.id && quote.content && quote.author) {
                    showResult('test1-result', 
                        `✅ SUCCESS: Quote fetched successfully\n` +
                        `ID: ${quote.id}\n` +
                        `Content: "${quote.content.substring(0, 100)}..."\n` +
                        `Author: ${quote.author.name}\n` +
                        `Categories: ${quote.categories?.length || 0}\n` +
                        `Sources: ${quote.sources?.length || 0}`, 
                        'success'
                    );
                } else {
                    showResult('test1-result', '❌ FAIL: Quote structure is invalid', 'error');
                }
            } catch (error) {
                showResult('test1-result', `❌ ERROR: ${error.message}`, 'error');
            }
        }

        // Test 2: Invalid Quote ID
        async function testInvalidQuoteId() {
            showLoading('test2-result');
            try {
                const quote = await window.ApiClient.getQuote(-1);
                if (quote === null) {
                    showResult('test2-result', '✅ SUCCESS: Invalid ID correctly returned null', 'success');
                } else {
                    showResult('test2-result', '❌ FAIL: Should return null for invalid ID', 'error');
                }
            } catch (error) {
                showResult('test2-result', `❌ ERROR: ${error.message}`, 'error');
            }
        }

        // Test 3: Non-existent Quote ID
        async function testNonExistentQuoteId() {
            showLoading('test3-result');
            try {
                const quote = await window.ApiClient.getQuote(999999);
                if (quote === null) {
                    showResult('test3-result', '✅ SUCCESS: Non-existent ID correctly returned null', 'success');
                } else {
                    showResult('test3-result', '❌ FAIL: Should return null for non-existent ID', 'error');
                }
            } catch (error) {
                showResult('test3-result', `❌ ERROR: ${error.message}`, 'error');
            }
        }

        // Test 4: String Quote ID
        async function testStringQuoteId() {
            showLoading('test4-result');
            try {
                const quote = await window.ApiClient.getQuote("abc");
                if (quote === null) {
                    showResult('test4-result', '✅ SUCCESS: String ID correctly returned null', 'success');
                } else {
                    showResult('test4-result', '❌ FAIL: Should return null for string ID', 'error');
                }
            } catch (error) {
                showResult('test4-result', `❌ ERROR: ${error.message}`, 'error');
            }
        }

        // Test 5: Null/Undefined Quote ID
        async function testNullQuoteId() {
            showLoading('test5-result');
            try {
                const quote1 = await window.ApiClient.getQuote(null);
                const quote2 = await window.ApiClient.getQuote(undefined);
                const quote3 = await window.ApiClient.getQuote();
                
                if (quote1 === null && quote2 === null && quote3 === null) {
                    showResult('test5-result', '✅ SUCCESS: Null/undefined IDs correctly returned null', 'success');
                } else {
                    showResult('test5-result', '❌ FAIL: Should return null for null/undefined IDs', 'error');
                }
            } catch (error) {
                showResult('test5-result', `❌ ERROR: ${error.message}`, 'error');
            }
        }

        // Test 6: Cache Functionality
        async function testCacheFunctionality() {
            showLoading('test6-result');
            try {
                const startTime1 = Date.now();
                const quote1 = await window.ApiClient.getQuote(1, true); // with cache
                const time1 = Date.now() - startTime1;

                const startTime2 = Date.now();
                const quote2 = await window.ApiClient.getQuote(1, true); // should use cache
                const time2 = Date.now() - startTime2;

                if (quote1 && quote2 && quote1.id === quote2.id && time2 < time1) {
                    showResult('test6-result', 
                        `✅ SUCCESS: Cache working correctly\n` +
                        `First call: ${time1}ms\n` +
                        `Cached call: ${time2}ms\n` +
                        `Cache speedup: ${Math.round((time1 - time2) / time1 * 100)}%`, 
                        'success'
                    );
                } else {
                    showResult('test6-result', 
                        `⚠️ WARNING: Cache may not be working optimally\n` +
                        `First call: ${time1}ms\n` +
                        `Second call: ${time2}ms`, 
                        'info'
                    );
                }
            } catch (error) {
                showResult('test6-result', `❌ ERROR: ${error.message}`, 'error');
            }
        }

        // Test 7: Multiple Valid IDs
        async function testMultipleValidIds() {
            showLoading('test7-result');
            try {
                const ids = [1, 2, 3, 4, 5];
                const promises = ids.map(id => window.ApiClient.getQuote(id));
                const quotes = await Promise.all(promises);
                
                const validQuotes = quotes.filter(quote => quote !== null);
                const successRate = (validQuotes.length / ids.length) * 100;
                
                showResult('test7-result', 
                    `✅ SUCCESS: Multiple IDs test completed\n` +
                    `Tested IDs: ${ids.join(', ')}\n` +
                    `Valid quotes: ${validQuotes.length}/${ids.length}\n` +
                    `Success rate: ${successRate}%\n` +
                    `Sample quote: "${validQuotes[0]?.content?.substring(0, 50)}..."`, 
                    successRate > 50 ? 'success' : 'error'
                );
            } catch (error) {
                showResult('test7-result', `❌ ERROR: ${error.message}`, 'error');
            }
        }

        // Run all tests
        async function runAllTests() {
            showLoading('all-tests-result');
            const tests = [
                testValidQuoteId,
                testInvalidQuoteId,
                testNonExistentQuoteId,
                testStringQuoteId,
                testNullQuoteId,
                testCacheFunctionality,
                testMultipleValidIds
            ];

            let passedTests = 0;
            const totalTests = tests.length;

            for (let i = 0; i < tests.length; i++) {
                try {
                    await tests[i]();
                    // Simple check if test passed (this is a basic implementation)
                    passedTests++;
                } catch (error) {
                    console.error(`Test ${i + 1} failed:`, error);
                }
                // Small delay between tests
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            showResult('all-tests-result', 
                `🎯 ALL TESTS COMPLETED\n` +
                `Passed: ${passedTests}/${totalTests}\n` +
                `Success rate: ${Math.round((passedTests / totalTests) * 100)}%\n` +
                `Check individual test results above for details.`, 
                passedTests === totalTests ? 'success' : 'info'
            );
        }

        // Initialize page
        window.addEventListener('load', function() {
            console.log('getQuote Test Suite loaded');
            console.log('ApiClient available:', !!window.ApiClient);
            console.log('getQuote method available:', typeof window.ApiClient?.getQuote);
        });
    </script>
</body>
</html>
