# Quotese.com 前端 Nginx 配置
# 支持语义化URL架构和SEO优化
# 版本：v2.0 - SEO重启实施
# 更新日期：2025年6月16日

server {
    listen 80;
    server_name quotese.com www.quotese.com;

    # 网站根目录
    root /var/www/quotese/frontend;
    index index.html;

    # 启用gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # ==================== 301重定向规则 ====================
    include /path/to/config/nginx_redirects.conf;

    # ==================== 语义化URL路由规则 ====================

    # 作者页面路由
    # /authors/ -> authors.html (作者列表页)
    location = /authors/ {
        try_files /authors.html /authors.html;
    }

    # /authors/{slug}/ -> author.html (作者详情页)
    location ~ ^/authors/([^/]+)/?$ {
        try_files /author.html /author.html;
    }

    # /authors/{slug}/quotes/ -> author.html (作者名言列表页)
    location ~ ^/authors/([^/]+)/quotes/?$ {
        try_files /author.html /author.html;
    }

    # 类别页面路由
    # /categories/ -> categories.html (类别列表页)
    location = /categories/ {
        try_files /categories.html /categories.html;
    }

    # /categories/{slug}/ -> category.html (类别详情页)
    location ~ ^/categories/([^/]+)/?$ {
        try_files /category.html /category.html;
    }

    # /categories/{slug}/quotes/ -> category.html (类别名言列表页)
    location ~ ^/categories/([^/]+)/quotes/?$ {
        try_files /category.html /category.html;
    }

    # 来源页面路由
    # /sources/ -> sources.html (来源列表页)
    location = /sources/ {
        try_files /sources.html /sources.html;
    }

    # /sources/{slug}/ -> source.html (来源详情页)
    location ~ ^/sources/([^/]+)/?$ {
        try_files /source.html /source.html;
    }

    # 名言页面路由
    # /quotes/ -> quotes.html (名言列表页)
    location = /quotes/ {
        try_files /quotes.html /quotes.html;
    }

    # /quotes/{id}/ -> quote.html (名言详情页)
    location ~ ^/quotes/(\d+)/?$ {
        try_files /quote.html /quote.html;
    }

    # 搜索页面路由
    # /search/ -> search.html (搜索页面)
    location = /search/ {
        try_files /search.html /search.html;
    }

    # ==================== 静态文件缓存优化 ====================

    # JavaScript和CSS文件 - 长期缓存
    location ~* \.(js|css)$ {
        expires 30d;
        add_header Cache-Control "public, no-transform, immutable";
        add_header Vary "Accept-Encoding";

        # 启用ETag
        etag on;

        # 处理跨域请求
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, OPTIONS";
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept";
    }

    # 图片文件 - 长期缓存
    location ~* \.(jpg|jpeg|png|gif|ico|svg|webp)$ {
        expires 90d;
        add_header Cache-Control "public, no-transform, immutable";
        add_header Vary "Accept-Encoding";

        # 启用ETag
        etag on;

        # 图片优化
        add_header X-Content-Type-Options "nosniff";
    }

    # 字体文件 - 长期缓存
    location ~* \.(woff|woff2|ttf|eot|otf)$ {
        expires 365d;
        add_header Cache-Control "public, no-transform, immutable";
        add_header Access-Control-Allow-Origin "*";

        # 启用ETag
        etag on;
    }

    # HTML文件 - 不缓存，确保内容更新
    location ~* \.html$ {
        expires -1;
        add_header Cache-Control "no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0";
        add_header Pragma "no-cache";

        # 安全头
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    }

    # ==================== 特殊文件处理 ====================

    # 网站地图 - 每日更新
    location = /sitemap.xml {
        expires 1d;
        add_header Cache-Control "public, max-age=86400";
        add_header Content-Type "application/xml; charset=utf-8";
    }

    # Robots.txt - 每周更新
    location = /robots.txt {
        expires 7d;
        add_header Cache-Control "public, max-age=604800";
        add_header Content-Type "text/plain; charset=utf-8";
    }

    # Favicon - 长期缓存
    location = /favicon.ico {
        expires 30d;
        add_header Cache-Control "public, no-transform";
        log_not_found off;
        access_log off;
    }

    # ==================== 错误页面处理 ====================

    # 自定义错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;

    # 404错误页面配置
    location = /404.html {
        internal;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }

    # 50x错误页面配置
    location = /50x.html {
        internal;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }

    # ==================== 安全配置 ====================

    # 隐藏Nginx版本信息
    server_tokens off;

    # 安全头配置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Permissions-Policy "geolocation=(), microphone=(), camera=()" always;

    # 禁止访问隐藏文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    # 禁止访问备份文件
    location ~ ~$ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # 禁止访问配置文件
    location ~* \.(conf|config|ini|log|bak|backup|old)$ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # ==================== 性能优化 ====================

    # 启用sendfile
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;

    # 设置客户端请求体大小限制
    client_max_body_size 10M;

    # 设置超时时间
    keepalive_timeout 65;
    client_body_timeout 12;
    client_header_timeout 12;
    send_timeout 10;

    # ==================== 默认路由处理 ====================

    # 默认路由 - 必须放在最后
    location / {
        # 首先尝试直接访问文件
        # 然后尝试目录
        # 最后回退到首页
        try_files $uri $uri/ /index.html;

        # 为SPA应用添加缓存控制
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }
}
